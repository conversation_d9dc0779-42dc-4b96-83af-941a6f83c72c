<div class="row w-100 h-100" style="overflow:auto;margin:0px;">
    <div class="col-md-12 pos">
      <mat-card class="w-100 mt-2">
        <mat-card-title class="">
          <div class="row">
            <div class="col-lg-12" style="padding-left: 40px;">
              <p class="d-flex align-items-center mb-1">
                <span class="ms-4 fs-5 text-primary d-inline-block  font-weight-bold custom-topic-size"
                  style=" border-bottom: 2px solid #2a295c; ">{{'kWaterConsumption' | translate}}</span>
                <span *ngIf="service.isSiteCreated"
                  class="ms-3 fs-6 display-text d-inline-block fw-light custom-content-size">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;{{service.startDate | date}}
                  {{'kTo' | translate}} {{service.endDate | date}}</span>
              </p>
            </div>
            <div class="col-lg-12 d-flex" style="padding-left: 40px;">
              <p class="dot f-1-0-0 list-view custom-content-size">{{'kWaterInfo' | translate}}.</p>
              <!-- <a class="link list-view cursor-pointer" ngbTooltip="{{'kClickHeretoViewMore' | translate}}" style="width: fit-content"
                (click)="openViewMore(viewMore)">{{'kviewmore' | translate}}</a> -->
            </div>
          </div>
        </mat-card-title>
        <mat-card-content>
          <div class="row">
            <div class="col-12 mb-2">
              <form>
                <!-- Chemical Products Table -->
                <ng-container *ngTemplateOutlet="chemicalProductsTable"></ng-container>
                
                <!-- Other Activities Consuming Water Table -->
                <div class="mt-4">
                  <ng-container *ngTemplateOutlet="otherActivitiesTable"></ng-container>
                </div>
              </form>
            </div>
          </div>
        </mat-card-content>
        <div class="row col-md-12 d-block">
          <div class="" style="text-align: end; padding-top: 20px;">
            <button type="submit" class="mx-auto px-5 py-2 border-0 fc-grey" (click)="submit()"
              style="border-radius: 1rem;background-color:#d2dd28;">{{'kSave' | translate}}</button>
          </div>
        </div>
      </mat-card>
    </div>
    <div class="energy-sec">
    </div>
  </div>

  <!-- Chemical Products Table Template -->
  <ng-template #chemicalProductsTable>
    <form [formGroup]="chemicalProductsForm" style="max-height: inherit;overflow: auto;margin-bottom: 10px;"
      *ngIf="chemicalTableDetail">
      <ng-container formArrayName="Rows">
        <table mat-table [dataSource]="chemicalDataSource" class="table-container">
          <ng-container matColumnDef="th4">
            <th mat-header-cell *matHeaderCellDef class="border text-center headercolor border-0" [attr.colspan]="12">
              {{'kChemicalNonChemicalProductsPurchased' | translate}}
            </th>
          </ng-container>
          <ng-container matColumnDef="pucs">
            <th mat-header-cell *matHeaderCellDef class="headercolor">{{'kProductsUsedForCleaningServices' | translate}}
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="name d-flex p-2"
              style="min-width: 200px; width: 100%; height: 55px;">
              <div class="d-flex align-items-center w-100">
                <p class="m-0 pl-1" style="font-weight: 500; width: 100%;">{{element.get('pucs').value}}</p>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="ppfrp">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kVolumeofProductsPurchasedReportedPeriod' | translate}} ({{'kKg' | translate}})
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" (change)="onChemicalProductValueChange($event.target.value, i, 'ppfrp')"
                  [appPreventMinues]="true" formControlName="ppfrp" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container matColumnDef="awcp">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kDoYouAddWaterToCleaningProduct' | translate}} ?
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
             <mat-form-field>
              <mat-select style="font-size: 14px;" formControlName="awcp"
                (selectionChange)="onDropdownChange($event.value, i, 'awcp')"
                placeholder="{{'kChooseAnyOne' | translate}}">
                <mat-option value="Yes">Yes</mat-option>
                <mat-option value="No">No</mat-option>
              </mat-select>
            </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="vwnas">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kVolumeOfWaterNeededPerActivityPerSingleUsage' | translate}}
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="vwnas"
                  (change)="onWaterFieldChange($event.target.value, i, 'vwnas')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="nuas">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kNumberofUsageperWeek' | translate}} (#)
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="nuas"
                  (change)="onWaterFieldChange($event.target.value, i, 'nuas')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="nwwdrp">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kNumberofWeeksWorkedduringtheReportingPeriod' | translate}} (#)
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="nwwdrp"
                  (change)="onWaterFieldChange($event.target.value, i, 'nwwdrp')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="iwh">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kIsWaterHeated' | translate}} ?
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
             <mat-form-field>
                <mat-select style="font-size: 14px;" formControlName="iwh"
                  (selectionChange)="onDropdownChange($event.value, i, 'iwh')"
                  placeholder="{{'kChooseAnyOne' | translate}}">
                  <mat-option value="Yes">Yes</mat-option>
                  <mat-option value="No">No</mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="iwhykh">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kIfWaterIsHeatedDoYouKnowHow' | translate}} ?
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" style="min-width: 150px;"
              class="text-center ">
              <!-- Show dropdown only when "Is the water heated?" is "Yes" -->
              <mat-form-field class="iwhykh-input-fields" *ngIf="element.get('iwh').value === 'Yes'">
                <mat-select style="font-size: 14px;" formControlName="iwhykh"
                  (selectionChange)="onDropdownChange($event.value, i, 'iwhykh')"
                  placeholder="{{'kChooseAnyOne' | translate}}">
                  <mat-option value="Electricity">{{'kElectricity' | translate}}</mat-option>
                  <mat-option value="Gas">{{'kGas' | translate}}</mat-option>
                  <mat-option value="Fuel">{{'kFuel' | translate}}</mat-option>
                </mat-select>
              </mat-form-field>
              <!-- Show disabled state when "Is the water heated?" is not "Yes" -->
              <span *ngIf="element.get('iwh').value !== 'Yes'" class="text-muted">-</span>
            </td>
          </ng-container>
          <ng-container matColumnDef="wcdrp">
            <th mat-header-cell *matHeaderCellDef class="pr-2 headercolor text-center">
              {{'kWaterConsumptionDuringReportingPeriod' | translate}} </th>

            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="text-center">
              <p>{{element.get('wcdrp').value | comma: element.get('wcdrp').value}}</p>
            </td>
          </ng-container>
          <ng-container matColumnDef="pgas">
            <th mat-header-cell *matHeaderCellDef class="pr-2 headercolor text-center">
              {{'kTCO2ePurchasedGoodsandServices' | translate}} </th>

            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="text-center">
              <p>{{element.get('pgas').value | comma: element.get('pgas').value}}</p>
            </td>
          </ng-container>

          <ng-container matColumnDef="wcdtp">
            <th mat-header-cell *matHeaderCellDef class="pr-2 headercolor text-center">
              {{'kWaterConsumptionDueToProducts' | translate}} </th>

            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="text-center">
              <p>{{element.get('wcdtp').value | comma: element.get('wcdtp').value}}</p>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="chemicaldisplayedColumnsTopHeader;sticky: true"></tr>

          <tr mat-header-row *matHeaderRowDef="chemicalDisplayedColumns;sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: chemicalDisplayedColumns;"></tr>
        </table>
      </ng-container>
    </form>
  </ng-template>

  <!-- Other Activities Consuming Water Template -->
  <ng-template #otherActivitiesTable>
    <form [formGroup]="otherActivitiesForm" style="max-height: inherit;overflow: auto;margin-bottom: 10px;"
      *ngIf="otherActivitiesTableDetail">
      <!-- Add button above the table -->
      <div class="row mb-3">
        <div class="col-md-12">
          <button mat-raised-button color="primary" (click)="addOtherActivityRow()" 
            ngbTooltip="{{'kAddarow' | translate}}">
            <mat-icon>add</mat-icon> {{'kAddActivity' | translate}}
          </button>
        </div>
      </div>
      
      <ng-container formArrayName="Rows">
        <table mat-table [dataSource]="otherActivitiesDataSource" class="table-container">
          <ng-container matColumnDef="th4">
            <th mat-header-cell *matHeaderCellDef class="border text-center headercolor border-0" [attr.colspan]="8">
              {{'kListOtherActivitiesConsumingWater' | translate}}
            </th>
          </ng-container>
          <ng-container matColumnDef="activity">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">{{'kActivity' | translate}}
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="text-center"
             >
              <div class="d-flex align-items-center w-100">
                <mat-form-field class="iwhykh-input-field">
                  <input matInput formControlName="activity">
                </mat-form-field>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="vwnas">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kVolumeOfWaterNeededPerActivityPerSingleUsage' | translate}}
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" 
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="vwnas"
                  (change)="onOtherActivityValueChange($event.target.value, i, 'vwnas')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="nuas">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kNumberofUsageperWeek' | translate}} (#)
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" 
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="nuas"
                  (change)="onOtherActivityValueChange($event.target.value, i, 'nuas')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="nwwdrp">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kNumberofWeeksWorkedduringtheReportingPeriod' | translate}} (#)
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" 
              class="text-center ">
              <mat-form-field class="inputValue">
                <input matInput type="number" min="0" formControlName="nwwdrp"
                  (change)="onOtherActivityValueChange($event.target.value, i, 'nwwdrp')"
                  [appPreventMinues]="true" [readonly]="false"
                  onKeyPress="if(this.value.length==7) return false;">
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="iwh">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kIsWaterHeated' | translate}} ?
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" 
              class="text-center ">
             <mat-form-field>
                <mat-select style="font-size: 14px;" formControlName="iwh"
                  (selectionChange)="onOtherActivityDropdownChange($event.value, i, 'iwh')"
                  placeholder="{{'kChooseAnyOne' | translate}}">
                  <mat-option value="Yes">Yes</mat-option>
                  <mat-option value="No">No</mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="iwhykh">
            <th mat-header-cell *matHeaderCellDef class="headercolor text-center">
              {{'kIfWaterIsHeatedDoYouKnowHow' | translate}} ?
            </th>
            <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" 
              class="text-center ">
              <div class="d-flex align-items-center justify-content-center w-100">
                <mat-form-field class="iwhykh-input-field" *ngIf="element.get('iwh').value === 'Yes'">
                  <input matInput formControlName="iwhykh">
                </mat-form-field>
              </div>
            <span *ngIf="element.get('iwh').value !== 'Yes'" class="text-muted">-</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="wcdrp">
          <th mat-header-cell *matHeaderCellDef class="pr-2 headercolor text-center">
            {{'kWaterConsumptionPerEquipmentDuringReportingPeriod' | translate}} </th>

          <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element"  class="text-center">
            <p>{{element.get('wcdrp').value | comma: element.get('wcdrp').value}}</p>
          </td>
        </ng-container>

        <ng-container matColumnDef="wcdtp">
          <th mat-header-cell *matHeaderCellDef class="pr-2 headercolor text-center">
            {{'kWaterConsumptionDueToProducts' | translate}} </th>

          <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element"  class="text-center">
            <p>{{element.get('wcdtp').value | comma: element.get('wcdtp').value}}</p>
          </td>
        </ng-container>

        <!-- Add delete column -->
        <ng-container matColumnDef="delete">
          <th mat-header-cell *matHeaderCellDef class="headercolor"> </th>
          <td mat-cell *matCellDef="let element; let i = index" style="min-width: 50px;">
            <button mat-icon-button color="warn" (click)="deleteOtherActivityRow(i)" 
              ngbTooltip="{{'kDeleterow' | translate}}">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="otherActivitiesTopHeader;sticky: true"></tr>
        <tr mat-header-row *matHeaderRowDef="otherActivitiesColumns;sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: otherActivitiesColumns;"></tr>
      </table>
    </ng-container>
  </form>
</ng-template>

  <!-- <ng-template #viewMore>
    <div class="modal-header">
      <div class="row ">
        <div class="col-12">
          <h2 class="my-2 text-info custom-topic-size">{{'kInformation' | translate}}</h2>
        </div>
      </div>
      <button mat-flat-button type="button" (click)="close()">
        <mat-icon style="font-size: 22px;" class="m-0">close</mat-icon>
      </button>
    </div>
    <div class="modal-body" style="height: fit-content;overflow: auto;max-height: 500px;">
      <div class="row">
        <div class="col-12">
          <ul>
            <li>
              <p class="list-view">{{'kProductReportedbelow' | translate}}</p>
            </li>
            <li>
              <p class="list-view">
                {{'kProductReportedbelow1' | translate}}
              </p>
            </li>
            <li>
              <p class="list-view">
                {{'kProductReportedbelow2' | translate}}
              </p>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button mat-flat-button type="button" class="mr-3" (click)="close()">{{'kClose' | translate}}</button>
    </div>
  </ng-template> -->
