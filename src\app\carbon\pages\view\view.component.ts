import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnDestroy, TemplateRef } from '@angular/core';
import { UntypedFormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { DataService } from 'src/app/shared/services/data.service';
import { SiteService } from 'src/app/shared/services/site.service';
import { GeneralInfoService } from '../../services/general-info.service';
import { QuestionresultsService } from '../../services/questionresults.service';
import { QuestionsService } from '../../services/questions.service';
import { ReportsComponent } from '../reports/reports.component';
import { LanguageTranslateService } from '../../services/language-translate.service';
import { TranslateService } from '@ngx-translate/core';
import {
  altDiaryAndProtiensOpID, beverageOpID, bikeOpId, buildingcfssnrOpID, buildingcfssrOpID,
  BusOpID, carOpID, CarpoolingOpID, dairyAndEggsOpID, electricBikeOpId,
  electricityOpId, fvcosOpID, grouptype, heatAndSteamOpId, logicsNonRenewableOpId,
  logicsRenewableOpId, meatAndFishOpID, motorCycleOpID, preparedFoodOpID, subwayOpID, supplyChainNonFoodOpID,
  supplygroup, supplySubGroup, taxiOpID, trainOpID, tramwayOpID, wasteNonFoodWasteOpID, wasteOpID
} from 'src/app/shared/constants/constant';
import { TitleCasePipe } from '@angular/common';
import * as _ from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { EmissionfactorserviceService } from '../../services/emissionfactorservice.service';
import { EmissiontypeService } from '../../services/emissiontype.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PptgenerateComponent } from 'src/app/shared/pptgenerate/pptgenerate.component';
import { MatDialog } from '@angular/material/dialog';

const kEmployeeCommuting = 'kEmployeeCommuting';
const kPlantbased = 'kPlantbased';
const kAnimalbased = 'kAnimalbased';
const kBeverageAndPreparedFood = 'kBeverageAndPreparedFood';
const kNonfooditems = 'kNonfooditems';
const kUpstreamDistAndTrans = 'kUpstreamDistAndTrans';
const kWaste = 'kWaste';
const kRefrigerant = 'kRefrigerant';
const kEnergySodexoService = 'kEnergySodexoService';
const kEnergySodexoFleet = 'kEnergySodexoFleet';
const kTotal = 'kTotal';
@Component({
  selector: 'app-view',
  templateUrl: './view.component.html',
  styleUrls: ['./view.component.scss']
})
export class ViewComponent implements OnInit, OnDestroy {
  @ViewChild('staticCsvPop') staticCsvPop: TemplateRef<any>;
  @ViewChild('panel') panel;
  @ViewChild(ReportsComponent) reports: ReportsComponent;
  @ViewChild(PptgenerateComponent) PptFileDownload: PptgenerateComponent;
  date = new Date();
  task: any = {
    name: 'kAll',
    completed: false,
    subtasks: [
      { name: 'kCradleToGate', completed: true },
      { name: 'kScope1And2', completed: true },
      { name: 'kToGrave', completed: true },
    ],
  };
  approvedPop = false;
  allComplete = true;
  popLoader = false;
  popDisabled = true;
  type: any;
  dialogRef: any;
  dialogRef2: any;
  showError = false;
  services: any;
  emissionFactList: any[];
  csvPdfOption: any;
  roleName: any;
  approvedStatus: any;
  approoverName: any;
  reportOption = 0;
  @Input()
  set views(view: any) {
    this.type = view;
  }
  get views(): any {
    return this.type;
  }
  questionResult: any[];
  siteCreated = this.service.isSiteCreated;
  isShow = false;
  employeeGroupType = grouptype;
  supplySubGroup = supplySubGroup;
  supplygroup = supplygroup;
  // groupNameList = [
  //   { id: 1, name: 'Employee Commuting', kvalue: 'kEmployeeCommuting' },
  //   { id: 2, name: 'Plant-based' , kvalue: 'kPlantbased'},
  //   { id: 3, name: 'Animal-based', kvalue: 'kAnimalbased'},
  //   { id: 4, name: 'Beverage & Prepared food' , kvalue: 'kBeverageAndPreparedFood'},
  //   { id: 5, name: 'Non-food items' , kvalue: 'kNonfooditems'},
  //   { id: 6, name: 'Upstream dist. & trans', kvalue: 'kUpstreamDistAndTrans'},
  //   { id: 7, name: 'Waste' , kvalue: 'kWaste'},
  //   { id: 8, name: 'Refrigerant' , kvalue: 'kRefrigerant'},
  //   { id: 9, name: 'Energy - Sodexo Service' , kvalue: 'kEnergySodexoService'},
  //   { id: 10, name: 'Energy - Sodexo Fleet' , kvalue: 'kEnergySodexoFleet'},
  //   { id: 11, name: 'Total', kvalue: 'kTotal' }];
  groupNameList = [
    { id: 1, name: 'Employee Commuting', kvalue: 'kEmployeeCommuting' },
    { id: 2, name: 'Supply Chain - Food', kvalue: 'kSupplyChainFood' },
    { id: 3, name: 'Supply Chain - Non-Food', kvalue: 'kSupplyChainNonFood' },
    { id: 4, name: 'Supply Chain - Distribution', kvalue: 'kSupplyChainDistribution' },
    { id: 5, name: 'Waste generated by our services', kvalue: 'kWasteGeneratedByOurServices' },
    { id: 6, name: 'Energy & Refrigerant used on the client site', kvalue: 'kEnergyRefrigerantUsedOnTheClientSite' },
    { id: 7, name: 'Outside of site impact (Sodexo Scope 1 & 2, business travel)', kvalue: 'kOutsideOfSiteImpact' },
    // { id: 5, name: 'Non-food items' , kvalue: 'kNonfooditems'},
    // { id: 6, name: 'Upstream dist. & trans', kvalue: 'kUpstreamDistAndTrans'},
    // { id: 7, name: 'Waste' , kvalue: 'kWaste'},
    // { id: 8, name: 'Refrigerant' , kvalue: 'kRefrigerant'},
    // { id: 9, name: 'Energy - Sodexo Service' , kvalue: 'kEnergySodexoService'},
    // { id: 10, name: 'Energy - Sodexo Fleet' , kvalue: 'kEnergySodexoFleet'},
    { id: 8, name: 'Total', kvalue: 'kTotal' }];
  backgroundColor = [
    {
      id: 1,
      color: 'rgba(249, 150, 30, 0.1)'
    },
    {
      id: 2,
      color: 'rgba(52, 184, 127, 0.1)'
    },
    {
      id: 3,
      color: 'rgba(86, 188, 226, 0.1)'
    },
    {
      id: 4,
      color: 'rgba(231, 48, 44, 0.1)'
    },
    {
      id: 5,
      color: 'rgba(40, 157, 50, 0.1)'
    },
    {
      id: 6,
      color: 'rgba(40, 117, 50, 0.1)'
    },
    {
      id: 7,
      color: 'rgba(40, 117, 50, 0.1)'
    },
    {
      id: 8,
      color: 'rgba(40, 130, 50, 0.1)'
    },
    {
      id: 9,
      color: 'rgba(40, 130, 50, 0.1)'
    },
    {
      id: 10,
      color: 'rgba(40, 130, 50, 0.1)'
    },
    {
      id: 11,
      color: 'rgba(40, 130, 50, 0.1)'
    },
  ];
  supplyChainColor = [
    {
      id: 1,
      color: 'rgba(52, 184, 127, 0.1)'
    },
    {
      id: 2,
      color: 'rgba(0, 0, 88, 0.1)'
    },
    {
      id: 3,
      color: 'rgba(0, 48, 41, 0.1)'
    },
    {
      id: 4,
      color: 'rgba(64, 107, 152, 0.1)'
    },
  ];

  @Output() goGeneral: EventEmitter<any> = new EventEmitter();
  generalInfo: any;
  groupName: any = [];
  subscription: Subscription = new Subscription();
  answerResult: any = [];
  language = this.service.languageIdentifier;
  activeQuesLists = {};
  kContactYorAdmin: any;
  EmailAddr = '';
  constructor(
    public service: GeneralInfoService,
    private questionResultService: QuestionresultsService,
    private questionService: QuestionsService,
    private emissionService: EmissionfactorserviceService,
    private emissiontype: EmissiontypeService,
    private dataService: DataService,
    private siteService: SiteService,
    private modalService: NgbModal,
    private router: Router,
    private fb: UntypedFormBuilder,
    private translate: TranslateService,
    private lts: LanguageTranslateService,
    private tcp: TitleCasePipe,
    private toaster: ToastrService,
    public oAuth: AuthService,
    private popService: MatDialog,
  ) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
    const csData$ = this.dataService.canReloadReport.subscribe((res: any) => {
      if (res === true) {
        this.answerResult = [];
        this.groupName = [];
        this.getSiteInfo();
        this.dataService.updateReload(false);
      }
    });
    this.subscription.add(csData$);
    this.oAuth.getRoleForCarbon().subscribe((el: any) => {
      this.roleName = el.role;
    });
  }

  ngOnInit(): void {
    this.getDetails();
  }
  getDetails() {
    this.generalInfo = this.service.siteInfo;
    this.services = this.service.siteInfo.services.map((service: any) =>
      service.language[this.language] ? service.language[this.language] : service.language['en']).toString();
    if (this.siteCreated) {
      this.getSiteInfo();
    } else {
      this.defaultValue();
    }
    this.generalInfo.siteManagers.forEach((item: any) => {
      if (item.role === 'Approver') {
        this.EmailAddr += item.email + ',';
      }
      console.log("role",item.role)
      console.log("this.EmailAddr",this.EmailAddr)
    });
    this.EmailAddr = this.EmailAddr.replace(/,*$/, '');
    let replaceText: any = this.translate.instant('kContactYorAdmin');
    this.kContactYorAdmin = replaceText.replace('$email', this.EmailAddr);
  }
  getSiteInfo() {
    const emissiontypelist$ = this.emissionService.
      getEmissionFactorByFilter({ country: this.service?.countryId, date: this.service?.startDate }).subscribe((res: any) => {
        this.emissionFactList = this.emissionService?.carbonEmissionFactor;
        this.getAllQuestions();
      }, (err: any) => {
        this.toaster.error('Unable to get emission lists', 'Error!');
      });
  }
  getAllQuestions() {
    this.questionService.getAll().subscribe((res: any) => {
      this.activeQuesLists = {};
      const activeQuesLists = res.filter((list: any) => list.is_active === true);
      const groupWithId = _.groupBy(activeQuesLists, 'topic');
      const innerGroupWithFormat = {};
      for (const ab in groupWithId) {
        if (innerGroupWithFormat.hasOwnProperty(ab)) {
          innerGroupWithFormat[ab] = _.groupBy(groupWithId[ab], 'question_format');
        } else {
          innerGroupWithFormat[ab] = {};
          innerGroupWithFormat[ab] = _.groupBy(groupWithId[ab], 'question_format');
        }
      }
      this.activeQuesLists = innerGroupWithFormat;
      this.getQuestionResult();
    }, (err: any) => {
      this.toaster.error('Unable to get list', 'Report summary');
    });
  }
  goBackHome(e: any) {
    this.goGeneral.emit();
  }
  getQuestionResult() {
    const payload = {
      topic: 1,
      site: this.service.siteId,
      date: this.service.endDate
    };
    this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
      if (res.data.length > 0) {
        this.isShow = true;
        const questionResult = res.data.filter((dat: any) => dat.question_format === 1);
        let answer;
        // let sum = 0;
        answer = JSON.parse(questionResult[0]?.answer);
        if (answer.length && typeof answer !== 'string') {
          const result = {
            id: 1,
            topic: 'Employee Commuting',
            value: answer
          };
          this.answerResult.push(result);
          const viewpayload = [
            {
              name: 'Employee Commuting',
              kvalue: 'kEmployeeCommuting',
              id: 1,
              groups: [
                {
                  name: 'Car',
                  kvalue: 'kCar',
                  optionArrayID: [...carOpID]
                },
                {
                  name: 'Taxi',
                  kvalue: 'kTaxi',
                  optionArrayID: [...taxiOpID]
                },
                {
                  name: 'Carpooling',
                  kvalue: 'kCarpooling',
                  optionArrayID: [...CarpoolingOpID]
                },
                {
                  name: 'Motorcycle',
                  kvalue: 'kMotorcycle',
                  optionArrayID: [...motorCycleOpID]
                },
                {
                  name: 'Bus',
                  kvalue: 'kBus',
                  optionArrayID: [...BusOpID]
                },
                {
                  name: 'Train',
                  kvalue: 'kTrain',
                  optionArrayID: [...trainOpID]
                },
                {
                  name: 'Subway',
                  kvalue: 'kSubway',
                  optionArrayID: [...subwayOpID]
                },
                {
                  name: 'Tramway',
                  kvalue: 'kTramway',
                  optionArrayID: [...tramwayOpID]
                },
                {
                  name: 'Electric Bike',
                  kvalue: 'kElectricBike',
                  optionArrayID: [...electricBikeOpId]
                },
                {
                  name: 'Bike',
                  kvalue: 'kBike',
                  optionArrayID: [...bikeOpId]
                },
              ],
              optionArrayID: []
            }
          ];
          viewpayload.map((view: any) => {
            const subgroupCollection = [];
            let totalEmission = 0;
            if (view.groups.length) {
              view.groups.map((group: any) => {
                const object = answer.filter((ans: any) => group.optionArrayID.includes(ans.question_id));
                const Co2e = object.reduce((initial: any, obj: any) => {
                  return initial += parseFloat(obj.co2e || 0);
                }, 0);
                totalEmission += Co2e;
                subgroupCollection.push({ name: group.kvalue, Co2e });
              });
              subgroupCollection.forEach((col: any) => {
                return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
              });
            }
            const empObj = {
              id: view.id,
              color: this.backgroundColor.find((e: any) => e.id === view.id).color,
              name: view.kvalue,
              totalCo2e: totalEmission,
              totalEmission: 0,
              subgroupName: [...subgroupCollection]
            };
            this.groupName.push(empObj);
          });
          // let answer1 = {};
          // answer1 = answer.reduce((r, a) => {
          //   r[a.group.name] = r[a.group.name] || [];
          //   r[a.group.name].push(a);
          //   return r;
          // }, Object.create(null));
          // (answer || [])?.forEach((e: any) => {
          //   if (e.co2e !== null) {
          //     sum += parseFloat(e.co2e);
          //   }
          // });
          // const subgroupName = [];
          // let totalEmission = 0;
          // for (const k in answer1) {
          //   if (Object.prototype.hasOwnProperty.call(answer1, k)) {
          //     let emissionSum = 0;
          //     (answer1[k] || []).forEach(a => {
          //       if (a.co2e !== null) {
          //         emissionSum += parseFloat(a.co2e);
          //       }
          //     });
          //     totalEmission += ((emissionSum / (sum || 1)) * 100);
          //     const obj = {
          //       name: this.employeeGroupType.find((group: any) => group.name === k).kvalue,
          //       Co2e: emissionSum,
          //       Emission: +((emissionSum / (sum || 1)) * 100)
          //     };
          //     subgroupName.push(obj);
          //   }
          // }
          // const empCommunity = {
          //   id: 1,
          //   color: this.backgroundColor.find((e: any) => {
          //     return e.id === 1;
          //   }).color,
          //   name: kEmployeeCommuting,
          //   totalCo2e: sum,
          //   totalEmission,
          //   subgroupName
          // };
          // this.groupName.push(empCommunity);
          // this.getList();
        } else {
          // tslint:disable-next-line:no-shadowed-variable
          const payload = {
            id: 1,
            color: this.backgroundColor.find((e: any) => {
              return e.id === 1;
            }).color,
            name: kEmployeeCommuting,
            totalCo2e: 0,
            totalEmission: 0,
            subgroupName: []
          };
          this.groupName.push(payload);
        }
        this.getSupplyQuestionResult();
      } else {
        this.isShow = true;
        // tslint:disable-next-line:no-shadowed-variable
        const payload = {
          id: 1,
          color: this.backgroundColor.find((e: any) => {
            return e.id === 1;
          }).color,
          name: kEmployeeCommuting,
          totalCo2e: 0,
          totalEmission: 0,
          subgroupName: []
        };
        this.groupName.push(payload);
        this.getSupplyQuestionResult();
      }
    },
      err => {
        this.defaultValue();
      });
  }
  getWasteQuestionResult() {
    const payload = {
      topic: 3,
      site: this.service.siteId,
      date: this.service.endDate
    };
    this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
      if (res.data.length > 0) {
        this.isShow = true;
        const questionResult = res.data;
        let answer = [];
        const value: any = [];
        const tablevalue = questionResult.filter((result: any) => result.question_format === 1);
        const formValue = questionResult.filter((result: any) => result.question_format === 0);
        answer = JSON.parse(tablevalue[0]?.answer);
        if (answer.length && typeof answer !== 'string') {
          const result = {
            id: 3,
            topic: 'Waste',
            value: { answer, formValue }
          };
          this.answerResult.push(result);
          // const tableQuestions = this.getContent(this.activeQuesLists['3']['1'][0]);
          // (answer || [])?.forEach((e: any) => {
          //   const optionsResult = this.getOption(tableQuestions.options, e.question_id);
          //   value.push({
          //     co2e: +(e.csp),
          //     name: optionsResult.name,
          //   });
          // });
          const viewpayload = [
            {
              name: 'Waste generated by our services',
              kvalue: 'kWasteGeneratedByOurServices',
              id: 5,
              groups: [
                {
                  name: 'Food waste',
                  kvalue: 'kFoodWaste',
                  id: 1,
                  optionArrayID: [...wasteOpID]
                },
                {
                  name: 'Non-food waste',
                  kvalue: 'kNonFoodWaste',
                  id: 2,
                  optionArrayID: [...wasteNonFoodWasteOpID]
                }
              ],
              optionArrayID: []
            },
          ];
          viewpayload.map((view: any) => {
            const subgroupCollection = [];
            let totalEmission = 0;
            if (view.groups.length) {
              view.groups.map((group: any) => {
                const object = answer.filter((ans: any) => group.optionArrayID.includes(ans.question_id));
                let Co2e = 0;
                if (group.id === 1) {
                  Co2e = object.reduce((initial: any, obj: any) => {
                    return initial += parseFloat(obj.cc || 0) +
                      parseFloat(obj.cm || 0) + parseFloat(obj.crecovery || 0) + parseFloat(obj.cl || 0) + parseFloat(obj.cu || 0);
                  }, 0);
                }
                if (group.id === 2) {
                  Co2e = object.reduce((initial: any, obj: any) => {
                    return initial += parseFloat(obj.cc || 0) +
                      parseFloat(obj.cm || 0) + parseFloat(obj.crecovery || 0) + parseFloat(obj.crecycled || 0)
                      + parseFloat(obj.cl || 0) + parseFloat(obj.cu || 0);
                  }, 0);
                }
                totalEmission += Co2e;
                subgroupCollection.push({ name: group.kvalue, Co2e });
              });
              subgroupCollection.forEach((col: any) => {
                return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
              });
            }
            const wasteObj = {
              id: view.id,
              color: this.backgroundColor.find((e: any) => e.id === view.id).color,
              name: view.kvalue,
              totalCo2e: totalEmission,
              totalEmission: 0,
              subgroupName: [...subgroupCollection]
            };
            this.groupName.push(wasteObj);
          });
          // this.bindValue(value, kWaste, 7);
        } else {
          // tslint:disable-next-line:no-shadowed-variable
          const payload = {
            id: 5,
            color: this.backgroundColor.find((e: any) => {
              return e.id === 5;
            }).color,
            name: 'kWasteGeneratedByOurServices',
            totalCo2e: 0,
            totalEmission: 0,
            subgroupName: []
          };
          this.groupName.push(payload);
        }
        this.getRefrigerantQuestionResult();
      } else {
        this.isShow = true;
        // tslint:disable-next-line:no-shadowed-variable
        const payload = {
          id: 5,
          color: this.backgroundColor.find((e: any) => {
            return e.id === 5;
          }).color,
          name: 'kWasteGeneratedByOurServices',
          totalCo2e: 0,
          totalEmission: 0,
          subgroupName: []
        };
        this.groupName.push(payload);
        this.getRefrigerantQuestionResult();
      }
    },
      err => {
        this.defaultValue();
      });
  }
  getEnergyQuestionResult() {
    const payload = {
      topic: 5,
      site: this.service.siteId,
      date: this.service.endDate
    };
    this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
      const energyobj = this.groupName.find((grp: any) => grp.id === 6);
      const index = this.groupName.findIndex((grp: any) => grp.id === 6);
      this.groupName.splice(index, 1);
      const viewpayload = [
        {
          name: 'Energy & Refrigerant used on the client site',
          kvalue: 'kEnergyRefrigerantUsedOnTheClientSite',
          id: 6,
          groups: [
            {
              name: 'Building consumption for Sodexo services renewable',
              kvalue: 'kBuildingConsumptionForSodexoServicesRenewable',
              optionArrayID: [...buildingcfssrOpID]
            },
            {
              name: 'Electricity',
              kvalue: 'kElectricity',
              optionArrayID: [...electricityOpId]
            },
            {
              name: 'Heat & Steam',
              kvalue: 'kHeatSteam',
              optionArrayID: [...heatAndSteamOpId]
            },
            {
              name: 'Logistics - renewable',
              kvalue: 'kLogisticsRenewable',
              optionArrayID: [...logicsRenewableOpId]
            },
            {
              name: 'Building consumption for Sodexo services - non-renewable',
              kvalue: 'kBuildingConsumptionForSodexoServicesNonRenewables',
              optionArrayID: [...buildingcfssnrOpID]
            },
            {
              name: 'Logistics - non renewable',
              kvalue: 'kLogisticsNonRenewable',
              optionArrayID: [...logicsNonRenewableOpId]
            }
          ],
          optionArrayID: []
        }
      ];
      if (res.data.length > 0) {
        this.isShow = true;
        const questionResult = res.data;
        let table1answer = [];
        let table2answer = [];
        const value: any = [];
        const tablevalue = questionResult.filter((result: any) => result.question_format === 1);
        const formValue = questionResult.filter((result: any) => result.question_format === 0);
        table2answer = JSON.parse(tablevalue[1]?.answer);
        table1answer = JSON.parse(tablevalue[0]?.answer);
        const findResult = {
          id: 5,
          topic: 'Energy',
          value: { table1answer, table2answer, formValue }
        };
        this.answerResult.push(findResult);
        const resultObj = [...table1answer, ...table2answer];
        viewpayload.map((view: any) => {
          const subgroupCollection = [...energyobj?.subgroupName];
          let totalEmission = energyobj?.totalCo2e || 0;
          if (view.groups.length) {
            view.groups.map((group: any) => {
              const object = resultObj.filter((ans: any) => group.optionArrayID.includes(ans.question_id));
              const Co2e = object.reduce((initial: any, obj: any) => {
                if (group.optionArrayID.includes('co_eg_001')) {
                  return initial += parseFloat(obj.co2emb || obj.co2e || 0);
                } else {
                  return initial += parseFloat(obj.co2elb || obj.co2e || 0);
                }
              }, 0);
              totalEmission += Co2e;
              subgroupCollection.push({ name: group.kvalue, Co2e });
            });
            subgroupCollection.forEach((col: any) => {
              return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
            });
          }
          const enerObj = {
            id: view.id,
            color: this.backgroundColor.find((e: any) => e.id === view.id).color,
            name: view.kvalue,
            totalCo2e: totalEmission,
            totalEmission: 0,
            subgroupName: [...subgroupCollection]
          };
          this.groupName.push(enerObj);
        });
        // if (table1answer.length) {
        //   let sumTCO2eLB = 0;
        //   table1answer.forEach((ans: any) => {
        //     if (ans.co2emb !== null) {
        //       sumTCO2eLB += parseFloat(ans.co2emb || 0);
        //     } else {
        //       sumTCO2eLB += parseFloat(ans.co2elb || 0);
        //     }
        //   });
        //   const subgroupName = [];
        //   const tableQuestions = this.getContent(this.activeQuesLists['5']['1'].find((list: any) => list.sequence === 1));
        //   table1answer.map((valu: any) => {
        //     const optionsResult = this.getOption(tableQuestions.options, valu.question_id);
        //     let val;
        //     if (valu.co2emb !== null) {
        //       val = {
        //         name: optionsResult.name,
        //         Co2e: valu.co2emb,
        //         Emission: ((parseFloat(valu.co2emb || 0) / (sumTCO2eLB || 1)) * 100)
        //       };
        //     } else {
        //       val = {
        //         name: optionsResult.name,
        //         Co2e: valu.co2elb,
        //         Emission: ((parseFloat(valu.co2elb || 0) / (sumTCO2eLB || 1)) * 100)
        //       };
        //     }
        //     subgroupName.push(val);
        //   });
        //   const paylo = {
        //     id: 9,
        //     color: this.backgroundColor.find((e: any) => {
        //       return e.id === 5;
        //     }).color,
        //     name: kEnergySodexoService,
        //     totalCo2e: sumTCO2eLB,
        //     totalEmission: 0,
        //     subgroupName
        //   };
        //   this.groupName.push(paylo);
        //   // });
        // } else {
        //   const payload023 = {
        //     id: 10,
        //     color: this.backgroundColor.find((e: any) => {
        //       return e.id === 6;
        //     }).color,
        //     name: kEnergySodexoFleet,
        //     totalCo2e: 0,
        //     totalEmission: 0,
        //     subgroupName: []
        //   };
        //   this.groupName.push(payload023);
        // }
        // if (table2answer.length && typeof table2answer !== 'string') {
        //   const result = {
        //     id: 5,
        //     topic: 'Energy',
        //     value: { table1answer, table2answer, formValue }
        //   };
        //   this.answerResult.push(result);
        //   const tableQuestions = this.getContent(this.activeQuesLists['5']['1'].find((list: any) => list.sequence === 2));
        //   (table2answer || [])?.forEach((e: any) => {
        //     const optionsResult = this.getOption(tableQuestions.options, e.question_id);
        //     value.push({
        //       co2e: (e.co2e),
        //       name: optionsResult.name
        //     });
        //   });
        //   this.bindValue(value, kEnergySodexoFleet, 10);
        // } else {
        //   // tslint:disable-next-line:no-shadowed-variable
        //   const payload = {
        //     id: 10,
        //     color: this.backgroundColor.find((e: any) => {
        //       return e.id === 6;
        //     }).color,
        //     name: kEnergySodexoFleet,
        //     totalCo2e: 0,
        //     totalEmission: 0,
        //     subgroupName: []
        //   };
        //   this.groupName.push(payload);
        // }
      } else {
        this.isShow = true;
        // tslint:disable-next-line:no-shadowed-variable
        // const payload = {
        //   id: 10,
        //   color: this.backgroundColor.find((e: any) => {
        //     return e.id === 6;
        //   }).color,
        //   name: kEnergySodexoFleet,
        //   totalCo2e: 0,
        //   totalEmission: 0,
        //   subgroupName: []
        // };
        // const payload01 = {
        //   id: 9,
        //   color: this.backgroundColor.find((e: any) => {
        //     return e.id === 5;
        //   }).color,
        //   name: kEnergySodexoService,
        //   totalCo2e: 0,
        //   totalEmission: 0,
        //   subgroupName: []
        // };
        // this.groupName.push(payload);
        // this.groupName.push(payload01);
        // viewpayload.map((view: any) => {
        //   const subgroupCollection = [...energyobj?.subgroupName];
        //   const totalEmission = energyobj?.totalCo2e || 0;
        //   if (view.groups.length) {
        //     view.groups.map((group: any) => {
        //       subgroupCollection.push({name: group.kvalue, Co2e: 0});
        //     });
        //     subgroupCollection.forEach((col: any) => {
        //       return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
        //     });
        //   }
        //   const enerObj = {
        //     id: view.id,
        //     color: this.backgroundColor.find((e: any) =>  e.id === view.id ).color,
        //     name: view.kvalue,
        //     totalCo2e: totalEmission,
        //     totalEmission: 0,
        //     subgroupName: [...subgroupCollection]
        //   };
        //   this.groupName.push(enerObj);
        // });
        const totalEmission = energyobj?.totalCo2e || 0;
        const enerObj = {
          id: 6,
          color: this.backgroundColor.find((e: any) => e.id === 6).color,
          name: 'kEnergyRefrigerantUsedOnTheClientSite',
          totalCo2e: totalEmission,
          totalEmission: 0,
          subgroupName: [...energyobj?.subgroupName]
        };
        this.groupName.push(enerObj);
      }
      this.pushTotal();
    },
      err => {
        this.defaultValue();
      });
  }
  getRefrigerantQuestionResult() {
    const payload = {
      topic: 4,
      site: this.service.siteId,
      date: this.service.endDate
    };
    this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
      if (res.data.length > 0) {
        this.isShow = true;
        const questionResult = res.data;
        let answer = [];
        const value: any = [];
        const tablevalue = questionResult.filter((result: any) => result.question_format === 1);
        const formValue = questionResult.filter((result: any) => result.question_format === 0);
        answer = JSON.parse(tablevalue[0]?.answer);
        if (answer.length && typeof answer !== 'string') {
          const result = {
            id: 4,
            topic: 'Refrigerant',
            value: answer
          };
          this.answerResult.push(result);
          const viewpayload = [
            {
              name: 'Energy & Refrigerant used on the client site',
              kvalue: 'kEnergyRefrigerantUsedOnTheClientSite',
              id: 6,
              groups: [
                {
                  name: 'Refrigerant',
                  kvalue: 'kRefrigerant',
                  optionArrayID: []
                }
              ],
              optionArrayID: []
            }
          ];
          viewpayload.map((view: any) => {
            const subgroupCollection = [];
            let totalEmission = 0;
            if (view.groups.length) {
              view.groups.map((group: any) => {
                const Co2e = answer.reduce((initial: any, obj: any) => {
                  return initial += parseFloat(obj.co2e || 0);
                }, 0);
                totalEmission += Co2e;
                subgroupCollection.push({ name: group.kvalue, Co2e });
              });
              subgroupCollection.forEach((col: any) => {
                return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
              });
            }
            const refriObj = {
              id: view.id,
              color: this.backgroundColor.find((e: any) => e.id === view.id).color,
              name: view.kvalue,
              totalCo2e: totalEmission,
              totalEmission: 0,
              subgroupName: [...subgroupCollection]
            };
            this.groupName.push(refriObj);
          });
          // answer.forEach((e: any) => {
          //   value.push({
          //     co2e: (e.co2e),
          // tslint:disable-next-line:max-line-length
          //     name: e.name ? JSON.parse(e.name).name : `${this.translate.instant(JSON.parse(e.equiType).kvalue)} - ${JSON.parse(e.emissiontype).name}`
          //   });
          // });
          // this.bindValue(value, kRefrigerant, 8);
        } else {
          // tslint:disable-next-line:no-shadowed-variable
          const payload = {
            id: 6,
            color: this.backgroundColor.find((e: any) => {
              return e.id === 6;
            }).color,
            name: 'kEnergyRefrigerantUsedOnTheClientSite',
            totalCo2e: 0,
            totalEmission: 0,
            subgroupName: [{
              name: 'kRefrigerant',
              Co2e: 0,
            }]
          };
          this.groupName.push(payload);
        }
        this.getEnergyQuestionResult();
      } else {
        this.isShow = true;
        // tslint:disable-next-line:no-shadowed-variable
        const payload = {
          id: 6,
          color: this.backgroundColor.find((e: any) => {
            return e.id === 6;
          }).color,
          name: 'kEnergyRefrigerantUsedOnTheClientSite',
          totalCo2e: 0,
          totalEmission: 0,
          subgroupName: [
            // {
            //   name: 'kRefrigerant',
            //   Co2e: 0,
            // }
          ]
        };
        this.groupName.push(payload);
        this.getEnergyQuestionResult();
      }
    },
      err => {
        this.defaultValue();
      });
  }
  bindValue(data, type, idValue) {
    let sum = 0;
    let totalEmission = 0;
    const childrenList: any = [];
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < data.length; i++) {
      if (data[i].co2e !== null) {
        sum += (+data[i].co2e);
      }
    }
    data.forEach((e: any) => {
      totalEmission += ((e.co2e / (sum || 1)) * 100);
      const value = {
        name: e.name,
        Co2e: e.co2e,
        Emission: +((e.co2e / (sum || 1)) * 100)
      };
      childrenList.push(value);
    });
    const payload = {
      id: idValue,
      color: this.backgroundColor.find((e: any) => {
        return e.id === idValue;
      }).color,
      name: type,
      totalCo2e: sum,
      totalEmission,
      subgroupName: childrenList
    };
    this.groupName.push(payload);
    // this.getList();
  }
  getList() {
    let sum = 0;
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < this.groupName.length; i++) {
      if (this.groupName[i].totalCo2e !== null) {
        sum += (+this.groupName[i].totalCo2e);
      }
    }
    // tslint:disable-next-line:no-shadowed-variable
    this.groupName = this.groupName.map((element: any) => {
      element.totalEmission = +((element.totalCo2e / (sum || 1)) * 100);
      return element;
    });
  }
  defaultValue() {
    if (this.groupName.length === 0) {
      this.groupName = [];
      this.isShow = true;
      this.groupNameList.forEach((element: any) => {
        const payload = {
          id: element.id,
          color: this.backgroundColor.find((e: any) => {
            return e.id === element.id;
          }).color,
          name: element.kvalue,
          totalCo2e: 0,
          totalEmission: 0,
          subgroupName: []
        };
        this.groupName.push(payload);
      });
    }
  }
  getSupplyQuestionResult() {
    const payload = {
      topic: 2,
      site: this.service.siteId,
      date: this.service.endDate
    };
    this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
      if (res.data.length > 1) {
        this.isShow = true;
        const questionResult = res.data.
          sort((a, b) => a.sequence - b.sequence);
        let answer = [];
        answer = JSON.parse(questionResult[1]?.answer);
        const answer2 = JSON.parse(questionResult[2]?.answer);
        if (answer.length && typeof answer !== 'string') {
          const result = {
            id: 2,
            topic: 'Supply Chain',
            value: { answer, answer2 }
          };
          this.answerResult.push(result);
          const viewpayload = [
            {
              name: 'Supply Chain - Food',
              kvalue: 'kSupplyChainFood',
              id: 2,
              groups: [
                {
                  name: 'Alternative dairy & proteins',
                  kvalue: 'kAlternativeDairyProteins',
                  optionArrayID: [...altDiaryAndProtiensOpID]
                },
                {
                  name: 'Beverages',
                  kvalue: 'kBeverages',
                  optionArrayID: [...beverageOpID]
                },
                {
                  name: 'Dairy & Eggs',
                  kvalue: 'kDairyEggs',
                  optionArrayID: [...dairyAndEggsOpID]
                },
                {
                  name: 'Fruits, vegetable, cereals, oils and spices',
                  kvalue: 'kFruitsVegetableCerealsOilsAndSpices',
                  optionArrayID: [...fvcosOpID]
                },
                {
                  name: 'Meat & Fish',
                  kvalue: 'kMeatFish',
                  optionArrayID: [...meatAndFishOpID]
                },
                {
                  name: 'Prepared foods',
                  kvalue: 'kPreparedFoods',
                  optionArrayID: [...preparedFoodOpID]
                },
              ],
              optionArrayID: []
            },
            {
              name: 'Supply Chain - Non-Food',
              kvalue: 'kSupplyChainNonFood',
              id: 3,
              groups: [
                {
                  name: 'Supply Chain - Non-Food',
                  kvalue: 'kSupplyChainNonFood',
                  optionArrayID: [...supplyChainNonFoodOpID]
                },
              ],
              optionArrayID: [],
            },
            // {
            //   name : 'Supply Chain - Distribution',
            //   kvalue: 'kSupplyChainDistribution',
            //   id: 4,
            //   groups: [
            //     {
            //       name : 'Supply Chain - Distribution',
            //       kvalue: 'kSupplyChainDistribution',
            //       optionArrayID : []
            //     },
            //   ],
            //   optionArrayID: [],
            // }
          ];
          viewpayload.map((view: any) => {
            const subgroupCollection = [];
            let totalEmission = 0;
            if (view.groups.length) {
              view.groups.map((group: any) => {
                const object = answer.filter((ans: any) => group.optionArrayID.includes(ans.question_id));
                const Co2e = object.reduce((initial: any, obj: any) => {
                  return initial += parseFloat(obj.pgas || 0);
                }, 0);
                totalEmission += Co2e;
                subgroupCollection.push({ name: group.kvalue, Co2e });
              });
              subgroupCollection.forEach((col: any) => {
                return col.Emission = (((col.Co2e || 0) / (totalEmission || 1)) * 100).toFixed(3);
              });
            }
            const supplyObj = {
              id: view.id,
              color: this.supplyChainColor.find((e: any) => e.id === view.id).color,
              name: view.kvalue,
              totalCo2e: totalEmission,
              totalEmission: 0,
              subgroupName: [...subgroupCollection]
            };
            this.groupName.push(supplyObj);
          });
          const updownEmission = answer2.reduce((r, a) => {
            if (a.udat !== null || a.udat !== undefined) {
              r += parseFloat(a.udat);
            }
            return r;
          }, 0);
          const empCommunity1 = {
            id: 4,
            color: this.supplyChainColor.find((e: any) => {
              return e.id === 4;
            }).color,
            name: 'kSupplyChainDistribution',
            totalCo2e: updownEmission,
            totalEmission: 0,
            subgroupName: []
          };
          this.groupName.push(empCommunity1);
        } else {
          this.groupName.push(...this.supplyDefaultShow());
        }
        this.getWasteQuestionResult();
      } else {
        this.isShow = true;
        this.groupName.push(...this.supplyDefaultShow());
        this.getWasteQuestionResult();
      }
    },
      err => {
        this.defaultValue();
      });
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  showErrorMessage() {
    this.dialogRef = this.modalService.open(this.staticCsvPop, { centered: true });
  }
  close() {
    this.showError = false;
    this.allComplete = true;
    this.task = {
      name: 'All',
      completed: false,
      subtasks: [
        { name: 'kCradleToGate', completed: true },
        { name: 'kScope1And2', completed: true },
        { name: 'kToGrave', completed: true },
      ],
    };
    this.dialogRef.close();
  }
  generateCsv() {
    if (this.allComplete) {
      this.getReports();
      this.close();
    } else {
      if (this.task.subtasks.every(t => t.completed === false)) {
        this.showError = true;
        return;
      } else {
        this.showError = false;
      }
      this.getReports();
      this.close();
    }
  }
  getReports() {
    // this.answerResult = [];
    // const anwers = {
    //   all: this.allComplete,
    //   cradleToGate: this.task.subtasks[0].completed,
    //   scope1_2: this.task.subtasks[1].completed,
    //   toGrave: this.task.subtasks[2].completed
    // };
    const anwers = {
      all: true,
      cradleToGate: true,
      scope1_2: true,
      toGrave: true
    };
    this.reports.dataOptions = anwers;
    this.reports.results = this.groupName;
    this.dataService.updateAnswer(this.answerResult);
    // this.reports.getSiteInfo();
  }
  updateAllComplete() {
    this.allComplete = this.task.subtasks != null && this.task.subtasks.every(t => t.completed);
    // tslint:disable-next-line:no-unused-expression
    if (this.task.subtasks.every(t => !t.completed)) {
      this.showError = true;
    } else {
      this.showError = false;
    }
  }

  someComplete(): boolean {
    if (this.task.subtasks == null) {
      return false;
    }
    return this.task.subtasks.filter(t => t.completed).length > 0 && !this.allComplete;
  }

  setAll(completed: boolean) {
    this.allComplete = completed;
    if (this.task.subtasks == null) {
      return;
    }
    this.task.subtasks.forEach(t => (t.completed = completed));
  }
  pushTotal() {
    this.getSodexoScopeImpact();
    let total = 0;
    let totalEmission = 0;
    this.groupName.map((group: any) => {
      total += parseFloat(group.totalCo2e || 0);
    });
    this.groupName.forEach((grp: any) => {
      grp.totalEmission = (grp.totalCo2e / (total || 1)) * 100;
      totalEmission += grp.totalEmission;
    });
    const payload1 = {
      id: 8,
      color: this.backgroundColor.find((e: any) => {
        return e.id === 8;
      }).color,
      name: kTotal,
      totalCo2e: total,
      totalEmission,
      subgroupName: []
    };
    this.groupName.push(payload1);
    this.groupName.sort((a: any, b: any) => a.id > b.id ? 0 : -1);
    const result = {
      id: 6,
      topic: 'emissionTableView',
      value: [...this.groupName]
    };
    this.answerResult.push(result);
  }
  goDashboard() {
    this.router.navigate(['carbon-dashboard'], { queryParams: { doc: JSON.stringify(this.groupName) } });
  }
  supplyDefaultShow() {
    const supplyDefaultArr = [];
    this.groupNameList.map((list: any) => {
      if ([2, 3, 4].includes(list.id)) {
        supplyDefaultArr.push({
          id: list.id,
          color: this.backgroundColor.find((e: any) => {
            return e.id === list.id;
          }).color,
          name: list.kvalue,
          totalCo2e: 0,
          totalEmission: 0,
          subgroupName: []
        });
      }
    });
    return supplyDefaultArr;
  }
  getContent(content) {
    // tslint:disable-next-line:no-string-literal
    return content.language[this.language] ? content.language[this.language] : content.language['en'];
  }
  getOption(options, questionId) {
    return options.find((option: any) => option.question_id === questionId);
  }
  getSodexoScopeImpact() {
    let revenues;
    try {
      revenues = JSON.parse(this.service?.generalInfo?.revenue);
    } catch (e) {
      revenues = this.service?.generalInfo?.revenue;
    }
    if (revenues) {
      const revenue = revenues?.revenue;
      let value;
      let value1;
      let value2;
      let value3;
      let gav;
      let ges1;
      let ges2;
      let gebt;
      let ges3fere;
      if (this.emissionFactList.length) {
        gav = (this.emissionFactList || []).find(e => e.emission_type === 264); // counrty annual revenue
        ges1 = (this.emissionFactList || []).find(e => e.emission_type === 265); // Group Emissions Scope 1
        ges2 = (this.emissionFactList || []).find(e => e.emission_type === 266); // Group Emissions Scope 2
        gebt = (this.emissionFactList || []).find(e => e.emission_type === 267); // Group Emissions Business travel
        // tslint:disable-next-line:max-line-length
        ges3fere = (this.emissionFactList || []).find(e => e.emission_type === 268); // Group Emissions Scope 3 Fuel and energy-related emissions
      }
      const gavValue = gav?.value || 0;
      const ges1Value = (ges1?.value || 0);
      const ges2Value = (ges2?.value || 0);
      const gebtValue = (gebt?.value || 0);
      const ges3fereValue = (ges3fere?.value || 0);
      value = (revenue * ges1Value || 0) / gavValue || 1;
      value1 = (revenue * ges2Value || 0) / gavValue || 1;
      value2 = (revenue * gebtValue || 0) / gavValue || 1;
      value3 = (revenue * ges3fereValue || 0) / gavValue || 1;
      const totalCo2e = value + value1 + value2 + value3;
      const result = {
        id: 0,
        topic: 'GeneralInfo',
        value: {
          scope1: value || 0,
          scope2: value1 || 0,
          gebt: value2 || 0,
          ges3fere: value3 || 0,
        }
      };
      this.answerResult.push(result);
      const pay = {
        id: 7,
        color: this.backgroundColor.find((e: any) => {
          return e.id === 7;
        }).color,
        name: 'kOutsideOfSiteImpact',
        totalCo2e: totalCo2e || 0,
        totalEmission: 0,
        subgroupName: [
        ]
      };
      this.groupName.push(pay);
    } else {
      const pay = {
        id: 7,
        color: this.backgroundColor.find((e: any) => {
          return e.id === 7;
        }).color,
        name: 'kOutsideOfSiteImpact',
        totalCo2e: 0,
        totalEmission: 0,
        subgroupName: [
        ]
      };
      this.groupName.push(pay);
    }
  }
  downloadCsvPptpopup() {
    if (this.service?.siteInfo?.approved) {
      this.approvedStatus = this.service?.siteInfo?.approved?.approved;
    }
    if (this.approvedStatus) {
      this.approvedPop = true;
    } else {
      this.approvedPop = false;
    }
    this.dialogRef2 = this.modalService.open(this.staticCsvPop, { centered: true });
  }
  selectOption() {

    if (this.reportOption && this.approvedStatus) {
      this.popDisabled = false;
    }
  }
  close1() {
    this.popDisabled = true;
    this.dialogRef2.close();
  }
  async submitDownloadFiles() {
    this.popLoader = true;
    if (this.reportOption === 1) {
      this.getReports();
      setTimeout(() => {
        this.popLoader = false;
        this.close1();
      }, 200);
    } else if (this.reportOption === 2) {
      await this.PptFileDownload.pptDownload(this.answerResult, this.groupName).then((resZ: any) => {
        this.popLoader = false;
        this.close1();
      }).catch(() => {
        this.popLoader = false;
      });
    }
  }

  /**
   * @description Send Email to approver for carbon validation
   */
  sendEmailToApprover() {
    // let EmailAddr = '';
    // this.generalInfo.siteManagers.forEach((item: any, index: any) => {
    //   if (item.role === 'Approver') {
    //     EmailAddr += item.email + ',';
    //     this.approoverName += item.name + ',';
    //   }
    // });
    const subject: any = `Reg: Approval Request for Site ID: ${this.generalInfo.siteId}`;
    const body: any = 'Please review and kindly appprove the carbon answers presented in the sea application';
    const mailtoLink: any = `mailto:${this.EmailAddr}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink);
  }
}
