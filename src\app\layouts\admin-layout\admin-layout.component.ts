import { AuthService } from 'src/app/shared/services/auth.service';
import { Component, HostListener, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { EventEmitterService } from 'src/app/shared/services/event-emitter.service';
import { DataService } from 'src/app/shared/services/data.service';
import { environment } from 'src/environments/environment';
import { CookieService } from 'ngx-cookie-service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ConfirmationWindowComponent } from 'src/app/shared/components/confirmation-window/confirmation-window.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConstantData } from 'src/app/shared/constants/constant';
import { DisableGlobalLoaderService } from 'src/app/shared/services/disable-global-loader.service';

@Component({
  selector: 'app-admin-layout',   
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  roles: string;
  isSuperAdmin = false;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth < 1441) {
      (document.body.style as any).zoom = '0.8';
    }
    else {
      (document.body.style as any).zoom = '1';
    }
  }
  showmenu = true;
  version: string = environment.version;
  currentYear = new Date().getFullYear();
  menus = [
    // {
    //   name: 'Dashboard',
    //   route: '/admin/dashboard',
    //   sortBy: {
    //     name: 'createdAt',
    //     value: 'desc'
    //   },
    //   icon: 'fas fa-tachometer-alt'

    // },
    // {
    //   name: 'Users',
    //   route: '/admin/users',
    //   icon: 'fas fa-users',
    //   sortBy: {
    //     name: 'username',
    //     value: 'asc'
    //   }
    // },
    {
      name: 'Site Managers',
      route: '/admin/managers',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-user-friends'
    },
    {
      name: 'Questions',
      route: '/admin/questions',
      icon: 'far fa-list-alt'
    }, {
      name: 'Sites',
      route: '/admin/sites',
      icon: 'fas fa-city',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
    }, {
      name: 'Services',
      route: '/admin/services',
      icon: 'fas fa-vector-square',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
    }, {
      name: 'Segments',
      route: '/admin/segments',
      icon: 'fas fa-cube',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
    },
    {
      name: 'Languages',
      route: '/admin/languages',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-language'

    },
    {
      name: 'Topics',
      route: '/admin/topics',
      icon: 'fas fa-layer-group'
    },
    {
      name: 'Notifications',
      route: '/admin/notifications',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fa fa-bell',
    },
    {
      name: 'Experts',
      route: '/admin/experts',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-hands-helping'
    },
    {
      name: 'Best Practices',
      route: '/admin/bestpractice',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-star'
    },
    // {
    //   name: 'Site Management',
    //   route: '/admin/site-management',
    //   limit: 10,
    //   offset: 0,
    //   sortBy: {
    //     name: 'createdAt',
    //     value: 'desc'
    //   },
    //   icon: 'fas fa-user-friends'
    // },
    {
      name: 'Support',
      route: '/admin/support',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-comments'
    },
    {
      name: 'SV Toms',
      route: '/admin/toms',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-globe'
    },
    {
      name: 'SV Project Data',
      route: '/admin/socialvalue',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-database'
    },
    // {
    //   name: 'imageupload',
    //   route: '/admin/imageupload',
    //   limit: 10,
    //   offset: 0,
    //   sortBy: {
    //     name: 'createdAt',
    //     value: 'desc'
    //   },
    //   icon: 'fas fa-globe'
    // },
    {
      name: 'Question Results',
      route: '/admin/questionresult',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-chart-line'
    },
    {
      name: 'Question Update',
      route: '/admin/questionUpdation',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-clipboard'
    },
    {
      name: 'SEA Store',
      route: '/admin/initiative',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-store'
    },
    {
      name: 'Downloaded Sea PPT',
      route: '/admin/downloadsodexopdf',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-file-powerpoint'
    },
    {
      name: 'Downloaded Sea Excel',
      route: '/admin/downloadseacsv',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-file-excel'
    },
    {
      name: 'Downloaded SeaStore PPT',
      route: '/admin/downloadseastoreppt',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-file-powerpoint'
    },
    {
      name: 'Downloaded SocialValue PPT',
      route: '/admin/downloadsvpdf',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-file-powerpoint'
    },
    {
      name: 'Uploaded files',
      route: '/admin/uploadedfiles',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-file-alt'
    },
    {
      name: "Field's Translate",
      route: '/admin/languageTranslate',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      icon: 'fas fa-language'
    },
    {
      name: 'System Fields',
      route: '/admin/systemfields',
      limit: 10,
      offset: 0,
      sortBy: {
        name: 'createdAt',
        value: 'desc'
      },
      // icon: 'fas fa-user-friends'
      icon: 'fas fa-cogs'   
    },
    // {
    //   name: 'Upload Video',
    //   route: '/admin/uploadVideo',
    //   sortBy: {
    //     name: 'createdAt',
    //     value: 'desc'
    //   },
    //   icon: 'fas fa-layer-group'
    // },
  ];
  constructor(
    private router: Router,
    public breakpointObserver: BreakpointObserver, private eventEmitterService: EventEmitterService,
    private dataService: DataService,
    private authService: AuthService,
    private cookieService: CookieService,
    private spinner: NgxSpinnerService,
    private modalService: NgbModal,
    private dgs: DisableGlobalLoaderService,
  ) {
    this.dgs.unload();
    if ((window.innerWidth < 1441) || ((screen.width < 1441))) {
      (document.body.style as any).zoom = '0.8';
    }
    // else {
    //   (document.body.style as any).zoom = '1';
    // }
  }

  ngOnInit() {
    this.roles = sessionStorage.getItem('role');
    if (!this.roles) {
      this.spinner.show();
      this.authService.getAdminToken().subscribe((res: any) => {
        console.log("Res:", res);
        
        this.roles = res.role;
        if (this.roles === 'SuperAdmin') {
          this.isSuperAdmin = true;
        }
        if (this.roles === 'SubAdmin') {
          this.menus = [
            {
              name: 'Site Managers',
              route: '/admin/managers',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-user-friends'
            },
            {
              name: 'Sites',
              route: '/admin/sites',
              icon: 'fas fa-city',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
            },
            {
              name: 'Experts',
              route: '/admin/experts',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-hands-helping'
            },
            {
              name: 'Best Practice Guides ',
              route: '/admin/bestpractice',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-book'
            },
            {
              name: 'Support',
              route: '/admin/support',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-comments'
            },
            // {
            //   name: 'SV Toms',
            //   route: '/admin/toms',
            //   limit: 10,
            //   offset: 0,
            //   sortBy: {
            //     name: 'createdAt',
            //     value: 'desc'
            //   },
            //   icon: 'fas fa-clipboard'
            // },
            // {
            //   name: 'SV Project Data',
            //   route: '/admin/socialvalue',
            //   limit: 10,
            //   offset: 0,
            //   sortBy: {
            //     name: 'createdAt',
            //     value: 'desc'
            //   },
            //   icon: 'fas fa-globe'
            // },
            {
              name: 'SEA Store',
              route: '/admin/initiative',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-globe'
            },
            {
              name: 'Downloaded Sea PPT',
              route: '/admin/downloadsodexopdf',
              limit: 10,
              offset: 0,
              sortBy: {
                name: 'createdAt',
                value: 'desc'
              },
              icon: 'fas fa-file-pdf'
            },
            // {
            //   name: 'Downloaded SocialValue PDF',
            //   route: '/admin/downloadsvpdf',
            //   limit: 10,
            //   offset: 0,
            //   sortBy: {
            //     name: 'createdAt',
            //     value: 'desc'
            //   },
            //   icon: 'fas fa-file-pdf'
            // },
            // {
            //   name: "Field's Translate",
            //   route: '/admin/languageTranslate',
            //   limit: 10,
            //   offset: 0,
            //   sortBy: {
            //     name: 'createdAt',
            //     value: 'desc'
            //   },
            //   icon: 'fas fa-language'
            // },
            // {
            //   name: 'imageupload',
            //   route: '/admin/imageupload',
            //   limit: 10,
            //   offset: 0,
            //   sortBy: {
            //     name: 'createdAt',
            //     value: 'desc'
            //   },
            //   icon: 'fas fa-globe'
            // }
          ];
        }
        this.spinner.hide();
      }, error => {
        this.spinner.hide();
        this.router.navigate(['login']);
        throw error;
      });
    }
    if (this.roles === 'SubAdmin') {
      this.menus = [
        {
          name: 'Site Managers',
          route: '/admin/managers',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-user-friends'
        },
        {
          name: 'Sites',
          route: '/admin/sites',
          icon: 'fas fa-city',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
        },
        {
          name: 'Experts',
          route: '/admin/experts',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-hands-helping'
        },
        {
          name: 'Best Practice Guides ',
          route: '/admin/bestpractice',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-book'
        },
        {
          name: 'Support',
          route: '/admin/support',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-comments'
        },
        // {
        //   name: 'SV Toms',
        //   route: '/admin/toms',
        //   limit: 10,
        //   offset: 0,
        //   sortBy: {
        //     name: 'createdAt',
        //     value: 'desc'
        //   },
        //   icon: 'fas fa-clipboard'
        // },
        // {
        //   name: 'SV Project Data',
        //   route: '/admin/socialvalue',
        //   limit: 10,
        //   offset: 0,
        //   sortBy: {
        //     name: 'createdAt',
        //     value: 'desc'
        //   },
        //   icon: 'fas fa-globe'
        // },
        {
          name: 'SEA Store',
          route: '/admin/initiative',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-globe'
        },
        {
          name: 'Downloaded Sea PPT',
          route: '/admin/downloadsodexopdf',
          limit: 10,
          offset: 0,
          sortBy: {
            name: 'createdAt',
            value: 'desc'
          },
          icon: 'fas fa-file-pdf'
        },
        // {
        //   name: 'Downloaded SocialValue PDF',
        //   route: '/admin/downloadsvpdf',
        //   limit: 10,
        //   offset: 0,
        //   sortBy: {
        //     name: 'createdAt',
        //     value: 'desc'
        //   },
        //   icon: 'fas fa-file-pdf'
        // },
        // {
        //   name: "Field's Translate",
        //   route: '/admin/languageTranslate',
        //   limit: 10,
        //   offset: 0,
        //   sortBy: {
        //     name: 'createdAt',
        //     value: 'desc'
        //   },
        //   icon: 'fas fa-language'
        // },
        // {
        //   name: 'imageupload',
        //   route: '/admin/imageupload',
        //   limit: 10,
        //   offset: 0,
        //   sortBy: {
        //     name: 'createdAt',
        //     value: 'desc'
        //   },
        //   icon: 'fas fa-globe'
        // }
      ];
    }


    this.breakpointObserver
      .observe(['(max-width: 1200px)'])
      .subscribe((state: BreakpointState) => {
        if (state.matches) {
          this.showmenu = false;
        } else {
          this.showmenu = true;
        }
      });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.roles === 'SuperAdmin') {
        this.isSuperAdmin = true;
      }
    }, 10)
  }

  isLinkActive(url: string): boolean {
    if (this.router.url.includes(url)) {
      return true;
    } else {
      return false;
    }
  }
  toggleLayout() {
    //  this.eventEmitterService.getTable();
    this.dataService.updateDataTable(1);
  }
  trackByFn(index, item) {
    return index;
  }
  logout() {
    const activateModal = this.modalService.open(ConfirmationWindowComponent);
    activateModal.componentInstance.title = ConstantData.logoutInfo.title;
    activateModal.componentInstance.content = ConstantData.logoutInfo.content;
    activateModal.componentInstance.updateState = this.cookieService.get('id_token');
    activateModal.result.then((res: any) => {
      this.updatLogout(res);
    }, (data) => { });

    // this.spinner.hide()
    // sessionStorage.clear();
    // this.cookieService.delete('token', '/');
    // this.router.navigate(['login']);
  }
  updatLogout(id) {
    this.spinner.show();
    if (id) {
      this.authService.logout(id).subscribe((res: any) => {
        sessionStorage.clear();
        window.location.href = res;
      }, error => {
        this.spinner.hide();
        throw error;
      });
    }
  }

  redirectCarbon() {
    this.router.navigate(['carbon/admin']);
  }

  goToAudit() {
    this.router.navigate(['audit-portal/approvedlist']);
  }
  // redirectCleaningCarbon() {
  //   this.router.navigate(['carbon-cleaning/admin']);
  // }
  redirectCarbonAdmin(Type?: any) {
    this.spinner.show();
    if (Type === '1') {
      this.router.navigate(['carbon/admin']);
    } 
    if (Type === '2') {
      this.router.navigate(['carbon-cleaning/admin']);
    }
  }
  goWaterAssessment() {
    this.router.navigate(['water/admin']);
  }
}
