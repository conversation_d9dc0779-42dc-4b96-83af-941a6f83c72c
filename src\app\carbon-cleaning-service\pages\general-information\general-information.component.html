<!-- <div class="row"></div> -->
<div class="row w-100 h-100 m-0" style="overflow: auto;">
    <button mat-icon-button style="position: absolute; top: 16px; right: 16px; z-index: 10;" class="text-dark" (click)="toggleSidenav()">
        <mat-icon>menu</mat-icon>
      </button>
    <div class="col-12 col-sm-12 col-md-12 pos">
        <mat-card class="w-100 mt-2">
            <mat-card-title class="">
                <div class="row">
                    <div class="col-12 col-xl-8" style="padding-left: 40px;">
                        <p class="mb-1 custom-topic-size"><span class="ms-4 fs-5 text-primary d-inline-block font-weight-bold"
                                style=" border-bottom: 2px solid #2a295c;font-size: 16px;">{{ 'kPreliminaryInformation' |
                                translate}}</span>
                            <span *ngIf="service.isSiteCreated"
                                class="ms-3 custom-subtopic-size text-muted d-inline-block fw-light">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;{{service.startDate
                                | date}}
                                {{ 'kTo' | translate}} {{service.endDate | date}}</span>
                        </p>
                    </div>
                    <div class="col-xl-4 col-12 d-flex justify-content-end" style="height: 37px !important;">
                        <!-- hide approver -->
                        <!-- <button class="btn btn-primary" type="button" (click)="isApprovedPopup()" *ngIf="isAprrover">
                            {{'kApprover' | translate}}
                        </button> -->
                    </div>
                    <!-- <div class="col-lg-12 d-flex" style="padding-left: 40px;">
                        <p class="dot f-1-0-0 list-view">{{'kPreliminaryDescription'| translate}}</p>
                        <a class="link list-view cursor-pointer pr-4" ngbTooltip="{{'kClickHeretoViewMore' | translate}}"
                            placement="left" style="width: fit-content" (click)="openViewMore(viewMore)">{{'kviewmore' |
                            translate}}</a>
                    </div> -->
                    <!-- <div class="col-xl-4 d-flex justify-content-center align-items-center">
                        <button type="submit" class="ml-auto px-3 py-2 d-block border-0 text-white shadow-none"
                            placement="left" ngbTooltip="This is reset the carbon data for Food and Catering Service"
                            (click)="resetSite(confirmPopup)"
                            style="border-radius: 1rem;font-size: 18px;background-color:#bb2520;">Reset</button>
                    </div> -->
                </div>
            </mat-card-title>
            <mat-card-content class="px-4 pt-4">
                <form [formGroup]="generalInformation" (ngSubmit)="formSubmit()">
                    <div class="row">
                        <div class="col-xl-4 mb-5">
                            <label for="client" class="form-label fs-6 fw-semibold display-text">{{ 'kSiteName' |
                                translate}}<span class="text-warning"></span></label>
                            <input type="text" formControlName="client" readonly class="form-control py-2 border-7"
                                id="client" placeholder="{{'kEnterYour' | translate}} {{ 'kSiteName' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5">
                            <label for="SiteName" class="form-label fs-6 fw-semibold display-text">{{'kSiteId' |
                                translate}}<span class="text-warning"></span></label>
                            <input type="text" formControlName="siteName" readonly class="form-control py-2 border-7"
                                id="SiteName" placeholder="{{'kEnterYour' | translate}} {{ 'kSiteId' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5">
                            <label for="siteAddress" class="form-label fs-6 fw-semibold display-text">{{'kSiteAddress' |
                                translate}}</label>
                            <input type="text" formControlName="siteAddress" readonly class="form-control py-2 border-7"
                                id="siteAddress"
                                placeholder="{{'kEnterYour' | translate}} {{ 'kSiteAddress' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5">
                            <label for="siteCountry" class="form-label fs-6 fw-semibold display-text">{{'kSiteCountry' |
                                translate}}</label>
                            <input type="text" formControlName="siteCountry" readonly class="form-control py-2 border-7"
                                id="siteCountry"
                                placeholder="{{'kEnterYour' | translate}} {{ 'kSiteCountry' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5">
                            <label for="zipcode" class="form-label fs-6 fw-semibold display-text">{{'kZipCode' |
                                translate}}</label>
                            <input type="number" formControlName="zipcode" min="0" readonly
                                class="form-control py-2 border-7" id="zipcode"
                                placeholder="{{'kEnterYour' | translate}} {{ 'kZipCode' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <label for="services" class="form-label fs-6 fw-semibold display-text">{{'kServices' |
                                translate}}</label>
                            <input type="text" formControlName="services" min="0" readonly
                                class="form-control py-2 border-7" id="services"
                                placeholder="{{'kEnterYour' | translate}} {{ 'kServices' | translate}}">
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="SurfaceAreaOfTheSite"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kSurfaceAreaOfTheSite' | translate}}
                                </label>
                                <!-- <mat-icon class="mb-1" placement="left"
                                    ngbTooltip="{{'kThisNumberCanBeRoughlyEstimated' | translate}}">
                                    info</mat-icon> -->
                            </div>
                            <input type="number" formControlName="SurfaceAreaOfTheSite" min="0"
                                class="form-control py-2 border-7" id="SurfaceAreaOfTheSite"
                                placeholder="{{'kEnterAValue' | translate}}">
                            <div *ngIf="submitted && f?.SurfaceAreaOfTheSite.errors?.required"
                                class="mt-2 invalid-feedback">
                                {{'kSurfaceAreaOfTheSite' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="SurfaceOfCleanedArea"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kSurfaceOfCleanedArea' |
                                    translate}}
                                </label>
                                <!-- <mat-icon class="mb-1" placement="left"
                                    ngbTooltip="{{'kThisNumberCanBeRoughlyEstimated' | translate}}">
                                    info</mat-icon> -->
                            </div>
                            <input type="number" formControlName="SurfaceOfCleanedArea" min="0"
                                class="form-control py-2 border-7" id="SurfaceOfCleanedArea"
                                placeholder="{{'kEnterAValue' | translate}}">
                            <div *ngIf="submitted && f?.SurfaceOfCleanedArea.errors?.required"
                                class="mt-2 invalid-feedback">
                                {{'kSurfaceOfCleanedArea' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="currency"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kCurrency' | translate}}
                                </label>
                                <mat-icon class="mb-1" placement="left" ngbTooltip="{{'kPleaseSelectTheCurrency' | translate}}">info</mat-icon>
                            </div>
                            <select class="form-control" id="currency" placeholder="currency"
                                formControlName="currency">
                                <option value="" [defaultSelected]="true" [disabled]="true">{{'kSelectACurrency' |
                                    translate}}</option>
                                <option *ngFor="let curr of currency; trackBy" [value]=curr.id>{{curr.name}}
                                </option>
                            </select>
                            <div *ngIf="submitted && f?.currency.errors?.required" class="mt-2 invalid-feedback">
                                {{'kSelectACurrency' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="CleaningRevenue"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kCleaningRevenue' | translate}}
                                </label>
                                <mat-icon class="mb-1" placement="left" ngbTooltip="{{'kPleaseIndicateWhichServices' | translate}}">info</mat-icon>
                            </div>
                            <select class="form-control" id="CleaningRevenue" placeholder="CleaningRevenue"
                                formControlName="CleaningRevenue">
                                <option value="" [defaultSelected]="true" [disabled]="true">{{'kSelectACleaningRevenue' |
                                    translate}}</option>
                                <option *ngFor="let cleaning of cleaningRevenues; trackBy" [value]=cleaning.id>{{cleaning.name}}
                                </option>
                            </select>
                            <div *ngIf="submitted && f?.CleaningRevenue.errors?.required" class="mt-2 invalid-feedback">
                                {{'kValueShouldBeGreater' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="NumberOfHoursSpentForCleaning"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kNumberOfHoursSpentForCleaning' |
                                    translate}}</label>
                                <mat-icon class="mb-1" placement="left" ngbTooltip="{{'kIndicateTheNumberOfHoursSpent' | translate}}">info</mat-icon>
                            </div>
                            <input type="number" formControlName="NumberOfHoursSpentForCleaning" min="0"
                                class="form-control py-2 border-7" id="NumberOfHoursSpentForCleaning"
                                placeholder="{{'kEnterAValue' | translate}}">
                            <div *ngIf="submitted && f?.NumberOfHoursSpentForCleaning.errors?.required" class="mt-2 invalid-feedback">
                                {{'kNumberOfHoursSpentForCleaning' | translate}} {{'kIsRequired' | translate}}
                            </div>                           
                        </div>
                        <div class="col-xl-4 mb-5 select-field">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="NoofPeopleSodexo"
                                    class="form-label fs-6 fw-semibold display-text display">{{'kNoOfPeopleSodexo' |
                                    translate}}
                                </label>
                                <mat-icon class="mb-1" placement="left" ngbTooltip="{{'kEnterTotalNoOfPeople' | translate}}">info</mat-icon>

                            </div>
                            <input type="number" formControlName="NoofPeopleSodexo" min="0"
                                class="form-control py-2 border-7" id="NoofPeopleSodexo"
                                placeholder="{{'kEnterAValue' | translate}}">
                            <div *ngIf="submitted && f?.NoofPeopleSodexo.errors?.required"
                                class="mt-2 invalid-feedback">
                                {{'kNoofPeopleSodexo' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                    </div>

                    <div class="row" *ngIf="this.oAuth.isRoleAdminOrSuper">
                        <div class="col-sm-6 col-md-6 col-lg-6">
                            <p class="mb-4 d-flex align-items-center"><span
                                    class="fs-6 pb-2 text-primary d-inline-block "
                                    style="font-weight: 500;">{{'kShowCalculation' | translate}}</span>
                                <mat-slide-toggle class="ml-3 mb-1" [color]="'primary'"
                                    [ngModelOptions]="{standalone: true}" [(ngModel)]="isChecked"></mat-slide-toggle>
                            </p>

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 col-md-6 col-lg-6">
                            <p class="mb-4 d-flex align-items-center"><span
                                    class="fs-6 pb-2 text-primary d-inline-block "
                                    style=" border-bottom: 1px solid #2a295c;font-weight: 500;">{{'kReportingperiod' |
                                    translate}}</span>
                                <mat-icon class="ml-1 mb-1" ngbTooltip="{{'kTheReportingPeriod' | translate}}">
                                    info</mat-icon> <span class="ml-3" style="font-weight: 600;"
                                    *ngIf="showMonth">({{month}} {{monthString | translate}}
                                    {{day}} {{dayString | translate}})</span>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xl-4 mb-4">
                            <label for="StartDate" class="form-label fs-6 fw-semibold display-text"><span
                                    class="text-warning">*</span>{{'kStartDate'| translate}}</label>
                            <div class="position-relative">
                                <input class="form-control py-2 pe-5 border-7" formControlName="startDate"
                                    [disabled]="isSiteCreated" (click)="startpicker.open()" id="StartDate"
                                    placeholder="{{'kStartYour' | translate}} {{'kStartDate'| translate}}"
                                    (dateChange)="dateStartChange($event)" [max]="maxStartDate"
                                    [matDatepicker]="startpicker">
                                <mat-datepicker-toggle matSuffix [for]="startpicker" class="align-date"></mat-datepicker-toggle>
                            </div>
                            <mat-datepicker #startpicker></mat-datepicker>
                            <div *ngIf="submitted && f?.startDate.errors?.required"
                                class="mt-2 invalid-feedback">
                               {{'kStartDate' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 mb-4">
                            <label for="endDate" class="form-label fs-6 fw-semibold display-text"><span
                                    class="text-warning">*</span>{{'kEndDate'|translate}}</label>
                            <div class="position-relative">
                                <input class="form-control py-2 pe-5 border-7" formControlName="endDate"
                                    [disabled]="isSiteCreated || endDateDisabled" (click)="endpicker.open()"
                                    id="endDate" placeholder="{{'kStartYour' | translate}} {{'kEndDate'| translate}}"
                                    (dateChange)="dateStartChange($event)" [matDatepicker]="endpicker"
                                    [min]="minEndDate" [max]="maxEndDate">
                                <mat-datepicker-toggle matSuffix [for]="endpicker" class="align-date">
                                </mat-datepicker-toggle>
                            </div>
                            <mat-datepicker #endpicker></mat-datepicker>
                            <div *ngIf="submitted && f?.endDate.errors?.required"
                                class="mt-2 invalid-feedback">
                                {{'kEndDate' | translate}} {{'kIsRequired' | translate}}
                            </div>
                        </div>
                        <div class="col-xl-4 d-flex justify-content-center align-items-center">
                            <button type="button" class="mr-3 ml-auto px-3 py-2 d-block border-0 text-white shadow-none"
                                placement="left" ngbTooltip="{{'kThisIsResetTheCarbon' | translate}}"
                                (click)="resetSite(confirmPopup)"
                                style="border-radius: 1rem;font-size: 18px;background-color:#bb2520;">{{'kReset' |
                                translate}}</button>
                            <button type="button" class="mx-auto px-3 py-2 d-block border-0 text-dark shadow-none"
                                (click)="submit()"
                                style="border-radius: 1rem;background-color:#d2dd28;">{{'kSaveContinue' |
                                translate}}</button>
                        </div>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
    <div class="general-sec">
    </div>
</div>
<ng-template #confirmPopup>
    <div class="modal-header">
        <div class="row ">
            <div class="col-12">
                <h2 class="my-2 text-dark">{{'kResetConfirmations' | translate}}</h2>
            </div>
        </div>

    </div>
    <div class="modal-body" style="height: 91px;overflow: auto;width: 433px;" *ngIf="!this.service.id">
        <p>{{'kCurrentlySiteCannotBeAbletoReset' | translate}}</p>
    </div>
    <div class="modal-body" style="height: 91px;overflow: auto;width: 433px;" *ngIf="this.service.id">
        <p>{{'kAreyousureyouwanttoreset' | translate}}</p>
    </div>
    <div class="modal-footer">
        <button mat-flat-button type="button" class="mr-1" (click)="close()">{{'kClose' | translate}}</button>
        <button mat-raised-button color="primary" type="button" class="mr-3" (click)="confirmReset()"
            *ngIf="this.service.id">{{'kConfirm'|translate}}</button>
    </div>
</ng-template>

<ng-template #viewMore>
    <div class="modal-header">
        <div class="row ">
            <div class="col-12">
                <h2 class="my-2 text-info custom-topic-size">{{'kInformation' | translate}}</h2>
            </div>
        </div>
        <button mat-flat-button type="button" (click)="close()">
            <mat-icon style="font-size: 22px;" class="m-0">close</mat-icon>
        </button>
    </div>
    <div class="modal-body" style="height: fit-content;overflow: auto;max-height: 500px;">
        <div class="row">
            <div class="col-12">
                <ul>
                    <li>
                        <p class="list-view">{{'kThisTabHelpsInCalculating' | translate}}</p>
                        <!-- <p class="list-view">Cet onglet permet de comprendre le type de site concerné par cette étude, et d'estimer les émissions si certaines données d'autres onglets ne seraient pas disponibles. Il permet aussi de calculer des ratios d'intensité</p> -->
                    </li>
                    <li>
                        <p class="list-view">
                            {{'kIfTheReportingIsDoneAnnually' | translate}}
                        </p>
                        <!-- <p class="list-view">
                Si le reporting est annuel, alors la période de reporting doit durer une année (12 mois). S'il est semestriel, alors la période doit couvrir 6 mois.
              </p> -->
                    </li>
                    <!-- <li>
              <p class="list-view">
                On each tab, please only answer in the yellow boxes + input comments. If data is unknown, please enter "Unknown" and explain in the comment section. If data is null, please enter 0.
              </p>
              <p class="list-view">
                Sur chacun des onglets, merci de remplir uniquement les cases en jaune + les commentaires dans la case indiquée. Si la donnée n'est pas connue, merci d'indiquer "Unknown" et d'expliquer dans la case commentaire. Si la donnée est nulle, merci d'indiquer 0
              </p>
            </li> -->
                </ul>

            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button mat-flat-button type="button" class="mr-3" (click)="close()">{{'kClose' | translate}}</button>
    </div>
</ng-template>

<ng-template #approved>
    <div class="modal-header">
        <div class="row ">
            <div class="col-12">
                <h2 class="my-2 text-info custom-topic-size">{{'kApproved' | translate}}</h2>
            </div>
        </div>
    </div>
    <div class="modal-body" style="min-width: 400px;overflow: auto;max-height: 500px;max-width: 500px;">
        <div class="row">
            <div class="col-12">
                <ul style="list-style: none;padding: 10px !important;">
                    <li>
                        <p class="list-view">{{'kApprovedPopupContent' | translate}}</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer justify-content-end">
        <button class="btn btn-danger" type="button" (click)="closeisAprroved()">{{'kClose' | translate}}</button>
        <button class="btn btn-primary" type="button" (click)="isAprroved()">{{'kSave' | translate}}</button>
    </div>
</ng-template>