import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CleaningGeneralInformationService } from '../../services/cleaning-general-information.service';
import { Router } from '@angular/router';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { NgxSpinnerService } from 'ngx-spinner';
import { Subscription } from 'rxjs';
import { CarbonCleaningQuestionsService } from '../../services/carbon-cleaning-questions.service';
import { CleaningquestionresultService } from '../../services/cleaningquestionresult.service';
import { EmissionfactorserviceService } from 'src/app/carbon/services/emissionfactorservice.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-water-consumption',
  templateUrl: './water-consumption.component.html',
  styleUrls: ['./water-consumption.component.scss']
})
export class WaterConsumptionComponent implements OnInit, OnDestroy {

  dialogRef: any;

  // Chemical Products Table Properties
  chemicalProductsForm: UntypedFormGroup;
  chemicalDataSource: MatTableDataSource<any>;
  chemicalDisplayedColumns: string[] = ['pucs', 'ppfrp', 'awcp', 'vwnas', 'nuas', 'nwwdrp', 'iwh', 'iwhykh', 'wcdrp', 'pgas', 'wcdtp'];
  chemicalTableDetail: any;
  chemicalQuestionId: any;
  chemicaldisplayedColumnsTopHeader = ['th4'];

  // Other properties
  questionResult: any = [];
  questionsList: any = [];
  subscription: Subscription = new Subscription();
  language = this.service.languageIdentifier;
  volUnit = 1;
  emissionValueForWaterConsumption: number = 0;

  // Add these properties to the class
  otherActivitiesForm: UntypedFormGroup;
  otherActivitiesDataSource: MatTableDataSource<any>;
  otherActivitiesColumns: string[] = ['activity', 'vwnas', 'nuas', 'nwwdrp', 'iwh', 'iwhykh', 'wcdrp', 'wcdtp', 'delete'];
  otherActivitiesTopHeader = ['th4'];
  otherActivitiesTableDetail: any;
  otherActivitiesQuestionId: any;

  constructor(
    private modalService: NgbModal,
    public service: CleaningGeneralInformationService,
    private route: Router,
    private fb: UntypedFormBuilder,
    private spinner: NgxSpinnerService,
    private questionService: CarbonCleaningQuestionsService,
    private questionResultService: CleaningquestionresultService,
    private emissionService: EmissionfactorserviceService,
    private toaster: ToastrService
  ) {

  }

  ngOnInit(): void {
    this.initializeChemicalProductsForm();
    this.initializeOtherActivitiesForm();
    this.getQuestionResult();
    
    // Get the emission factor for water consumption
    const emissionValueForWaterConsumption = (this.emissionService.carbonEmissionFactor || [])
      .find(e => e?.description.includes('Water Consumption') || e?.description.includes('water consumption'));
    this.emissionValueForWaterConsumption = emissionValueForWaterConsumption?.value || 0;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  initializeChemicalProductsForm() {
    // Initialize the chemical products form
    this.chemicalProductsForm = this.fb.group({
      Rows: this.fb.array([])
    });
  }

  initializeOtherActivitiesForm() {
    // Initialize the other activities form
    this.otherActivitiesForm = this.fb.group({
      Rows: this.fb.array([])
    });
  }

  getQuestionResult() {
    const payload = {
      topic: 2,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      date: this.service.endDate
    };

    const results$ = this.questionResultService.getCleaningQuestionsResultsById(payload).subscribe((res: any) => {
      this.spinner.hide();
      this.questionResult = res?.data || [];
      this.getQuestions(2, res);
    },
      err => {
        this.spinner.hide();
        this.getQuestions(2);
      });
    this.subscription.add(results$);
  }

  getQuestions(type, res?: any) {
    this.spinner.show();
    const questions$ = this.questionService.getCleaningQuestionByTopicId(type).subscribe(data => {
      this.spinner.hide();
      this.questionsList = data;
      this.initiateChemicalProductsTable();
      this.initiateOtherActivitiesTable(); // Add this line
    }, (err: any) => {
      this.spinner.hide();
      throw err;
    });
    this.subscription.add(questions$);
  }

  openViewMore(tem) {
    this.dialogRef = this.modalService.open(tem, { centered: true });
  }

  initiateChemicalProductsTable() {
    this.spinner.show();

    // Find the chemical products question by sequence number
    const chemicalQuestion = this.questionsList.find(q =>
      q.sequence === 6 && // Chemical products have sequence 6
      q.question_format === 1 &&
      q.is_active
    );

    if (!chemicalQuestion || !chemicalQuestion.options || !chemicalQuestion.options.length) {
      this.spinner.hide();
      return;
    }

    this.chemicalTableDetail = chemicalQuestion;
    const chemicalOptions = chemicalQuestion.options || [];

    // Find the question result ID if it exists
    if (this.questionResult && this.questionResult.length > 0) {
      const chemicalQuestionResult = this.questionResult.find(result =>
        result.question === chemicalQuestion.id
      );

      if (chemicalQuestionResult) {
        this.chemicalQuestionId = chemicalQuestionResult.id;
      }
    }

    // Create the form with dynamic data from API
    this.chemicalProductsForm = this.fb.group({
      Rows: this.fb.array((chemicalOptions || []).map((val, i) =>
        this.createChemicalProductRow(val)
      ))
    });

    this.chemicalDataSource = new MatTableDataSource(
      (this.chemicalProductsForm.get('Rows') as UntypedFormArray).controls
    );

    this.spinner.hide();
  }

  createChemicalProductRow(option: any) {
    let volume = 0;
    let pgas = 0;
    let awcp = '';
    let vwnas = 0;
    let nuas = 0;  
    let nwwdrp = 0; 
    let iwh = '';
    let iwhykh = '';
    let wcdrp = 0;
    let wcdtp = 0; 

    // Check if we have existing data for this product
    if (this.questionResult && this.questionResult.length > 0) {
      const chemicalQuestionResult = this.questionResult.find(result =>
        result.question === this.chemicalTableDetail.id
      );

      if (chemicalQuestionResult) {
        let answerData;
        try {
          answerData = typeof chemicalQuestionResult.answer === 'string' ?
            JSON.parse(chemicalQuestionResult.answer) : chemicalQuestionResult.answer;
        } catch (e) {
          answerData = [];
        }

        // Find the specific product answer
        const productAnswer = Array.isArray(answerData) ?
          answerData.find((a: any) => a.question_id === option.question_id) : null;

        if (productAnswer) {
          volume = productAnswer.volume || productAnswer.ppfrp || 0;
          pgas = productAnswer.pgas || 0;
          awcp = productAnswer.awcp || '';
          vwnas = productAnswer.vwnas || 0;
          nuas = productAnswer.nuas || 0;  
          nwwdrp = productAnswer.nwwdrp || 0; 
          iwh = productAnswer.iwh || '';
          iwhykh = productAnswer.iwhykh || '';
          wcdrp = productAnswer.wcdrp || 0; 
          wcdtp = productAnswer.wcdtp || 0;
        }
      }
    }

    // Get the translated name if available
    const translatedOption = this.chemicalTableDetail.language && this.chemicalTableDetail.language[this.language] ?
      this.chemicalTableDetail.language[this.language].options.find((o: any) => o.question_id === option.question_id) :
      null;

    return this.fb.group({
      productId: new UntypedFormControl(option.question_id),
      pucs: new UntypedFormControl(translatedOption?.name || option.name),
      ppfrp: new UntypedFormControl(volume),
      awcp: new UntypedFormControl(awcp), // Do you add water to the cleaning product?
      vwnas: new UntypedFormControl(vwnas), // Volume of Water Needed per Activity per Single Usage (m³)
      nuas: new UntypedFormControl(nuas), // Number of Usage per Week (#)
      nwwdrp: new UntypedFormControl(nwwdrp), // Number of Weeks Worked during the Reporting Period (#)
      iwh: new UntypedFormControl(iwh), // Is the water heated?
      iwhykh: new UntypedFormControl(iwhykh), // If the water is heated, do you know how it is heated?
      pgas: new UntypedFormControl(pgas),
      wcdrp: new UntypedFormControl(wcdrp), // Water Consumption during Reporting Period (m³)
      wcdtp: new UntypedFormControl(wcdtp), // Products Equipment Purchased for Cleaning Services
      question_id: new UntypedFormControl(option.question_id),
      toolTip: new UntypedFormControl(translatedOption?.description || option.description || '')
    });
  }

  onChemicalProductValueChange(value: any, index: number, field?: string) {
    const targetForm = ((this.chemicalProductsForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);

    // Prevent negative values for numeric fields only (not dropdown fields)
    if (field && ['ppfrp', 'vwnas'].includes(field) && parseFloat(value) < 0) {
      targetForm.get(field).setValue(0);
      return;
    }

    // Mark the form as dirty to enable save button
    this.chemicalProductsForm.markAsDirty();

    // Update the data source if needed
    this.updateChemicalDataSource();

    // Calculate emissions based on product volume
    const currentRow = this.chemicalTableDetail?.options[index] || {};
    const emissionType = currentRow.emissionType || {};
    console.log("this.emissionService.carbonEmissionFactor", this.emissionService.carbonEmissionFactor);

    if(!this.emissionValueForWaterConsumption){
      const emissionValueForWaterConsumption = (this.emissionService.carbonEmissionFactor || []).find(e => e?.description.includes('Water Consumption') || e?.description.includes('water consumption'));
      console.log("emissionValueForWaterConsumption: ", emissionValueForWaterConsumption);
      this.emissionValueForWaterConsumption = emissionValueForWaterConsumption?.value || 0;
    }
    
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);

    targetForm.get('wcdrp').setValue(
      ((targetForm.get('vwnas').value || 0) * (targetForm.get('nuas').value || 0) * (targetForm.get('nwwdrp').value || 0)).toFixed(3)
    );
    
    // Calculate pgas (tCO2e Purchased Goods and Services)
    targetForm.get('pgas').setValue(
      ((targetForm.get('ppfrp').value || 0) * (emissionValue?.value || 0) / 1000).toFixed(3)
    );

    targetForm.get('wcdtp').setValue(
      ((targetForm.get('wcdrp').value || 0) * (this.emissionValueForWaterConsumption || 0)).toFixed(3)
    );
  }

  onWaterFieldChange(value: any, index: number, field: string) {
    this.onChemicalProductValueChange(value, index, field);
  }

  onDropdownChange(value: any, index: number, field: string) {
    const targetForm = ((this.chemicalProductsForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);

    // Special handling for "Is the water heated?" dropdown
    if (field === 'iwh') {
      if (value !== 'Yes') {
        // Clear the "how it is heated" field when "Is the water heated?" is not "Yes"
        targetForm.get('iwhykh').setValue('');
      }
    }

    this.onChemicalProductValueChange(value, index, field);
  }

  updateChemicalDataSource() {
    this.chemicalDataSource = new MatTableDataSource(
      (this.chemicalProductsForm.get('Rows') as UntypedFormArray).controls
    );
  }

  saveChemicalProducts() {
    if (!this.chemicalProductsForm || !this.chemicalTableDetail) {
      return;
    }

    const topicId = 2;
    const chemicalPayload: any = {
      answer: this.chemicalProductsForm.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: topicId,
      question: this.chemicalTableDetail.id,
      question_format: this.chemicalTableDetail.question_format,
      sequence: this.chemicalTableDetail.sequence,
      unit: { weight: this.volUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };

    if (this.chemicalQuestionId) {
      chemicalPayload.id = this.chemicalQuestionId;
    }

    this.spinner.show();

    if (this.chemicalQuestionId) {
      // Update existing record
      const saveChemical$ = this.questionResultService.updateQuestionsResults(chemicalPayload, this.chemicalQuestionId).subscribe(
        (res: any) => {
          this.spinner.hide();
          this.toaster.success('Chemical products data updated successfully');
        },
        (err: any) => {
          this.spinner.hide();
          this.toaster.error('Error updating chemical products data');
          console.error(err);
        }
      );
      this.subscription.add(saveChemical$);
    } else {
      // Create new record
      const saveChemical$ = this.questionResultService.createQuestionResults(chemicalPayload).subscribe(
        (res: any) => {
          this.spinner.hide();
          this.toaster.success('Chemical products data saved successfully');
          if (res?.id) {
            this.chemicalQuestionId = res.id;
          }
        },
        (err: any) => {
          this.spinner.hide();
          this.toaster.error('Error saving chemical products data');
          console.error(err);
        }
      );
      this.subscription.add(saveChemical$);
    }
  }

  initiateOtherActivitiesTable() {
    this.spinner.show();

    // Create just one default empty row initially
    const defaultActivities = [
      { activity: '', vwnas: 0, nuas: 0, nwwdrp: 0, iwh: '', iwhykh: '', wcdrp: 0 }
    ];

    // Create the form with default rows
    this.otherActivitiesForm = this.fb.group({
      Rows: this.fb.array(defaultActivities.map(activity => this.createOtherActivityRow(activity)))
    });

    this.otherActivitiesDataSource = new MatTableDataSource(
      (this.otherActivitiesForm.get('Rows') as UntypedFormArray).controls
    );

    this.otherActivitiesTableDetail = true; // Set to true to display the table
    this.spinner.hide();
  }

  createOtherActivityRow(data: any) {
    return this.fb.group({
      activity: new UntypedFormControl(data.activity || ''),
      vwnas: new UntypedFormControl(data.vwnas || 0),
      nuas: new UntypedFormControl(data.nuas || 0),
      nwwdrp: new UntypedFormControl(data.nwwdrp || 0),
      iwh: new UntypedFormControl(data.iwh || ''),
      iwhykh: new UntypedFormControl(data.iwhykh || ''),
      wcdrp: new UntypedFormControl(data.wcdrp || 0),
      wcdtp: new UntypedFormControl(data.wcdtp || 0) // Add the new field
    });
  }

  onOtherActivityValueChange(value: any, index: number, field: string) {
    const targetForm = ((this.otherActivitiesForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);

    // Prevent negative values for numeric fields
    if (field && ['vwnas', 'nuas', 'nwwdrp'].includes(field) && parseFloat(value) < 0) {
      targetForm.get(field).setValue(0);
      return;
    }

    // Mark the form as dirty to enable save button
    this.otherActivitiesForm.markAsDirty();

    // Calculate water consumption
    targetForm.get('wcdrp').setValue(
      ((targetForm.get('vwnas').value || 0) * (targetForm.get('nuas').value || 0) * (targetForm.get('nwwdrp').value || 0)).toFixed(3)
    );
    
    // Calculate water consumption due to products (using the emission factor)
    targetForm.get('wcdtp').setValue(
      ((targetForm.get('wcdrp').value || 0) * (this.emissionValueForWaterConsumption || 0)).toFixed(3)
    );

    // Update the data source
    this.updateOtherActivitiesDataSource();
  }

  onOtherActivityDropdownChange(value: any, index: number, field: string) {
    const targetForm = ((this.otherActivitiesForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);

    // Special handling for "Is the water heated?" dropdown
    if (field === 'iwh') {
      if (value !== 'Yes') {
        // Clear the "how it is heated" field when "Is the water heated?" is not "Yes"
        targetForm.get('iwhykh').setValue('');
      }
    }

    this.onOtherActivityValueChange(value, index, field);
  }

  updateOtherActivitiesDataSource() {
    this.otherActivitiesDataSource = new MatTableDataSource(
      (this.otherActivitiesForm.get('Rows') as UntypedFormArray).controls
    );
  }

  submit() {
    // Save chemical products data before navigating
    this.saveChemicalProducts();
    this.saveOtherActivities(); // Add this line

    // Navigate to next page
    setTimeout(() => {
      this.route.navigate([`carbon-cleaning/${this.service.siteId}/waste`]);
    }, 1000);
  }

  saveOtherActivities() {
    if (!this.otherActivitiesForm) {
      return;
    }

    // Filter out empty rows (where activity is empty)
    const validRows = (this.otherActivitiesForm.get('Rows') as UntypedFormArray).controls
      .filter(row => row.get('activity').value && row.get('activity').value.trim() !== '')
      .map(row => row.value);

    if (validRows.length === 0) {
      return; // Don't save if there are no valid rows
    }

    const topicId = 2;
    const payload: any = {
      answer: validRows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: topicId,
      question_format: 1,
      sequence: 7, // Use a different sequence number than chemical products
      unit: { water: 'm³' },
      isApprovedBy: true,
      service: "Soft FM"
    };

    this.spinner.show();
    const saveActivities$ = this.questionResultService.createQuestionResults(payload).subscribe(
      (res: any) => {
        this.spinner.hide();
        this.toaster.success('Other activities data saved successfully');
      },
      (err: any) => {
        this.spinner.hide();
        this.toaster.error('Error saving other activities data');
        console.error(err);
      }
    );
    this.subscription.add(saveActivities$);
  }

  // Add method to add a new row
  addOtherActivityRow() {
    const newActivity = { activity: '', vwnas: 0, nuas: 0, nwwdrp: 0, iwh: '', iwhykh: '', wcdrp: 0 };
    const rows = this.otherActivitiesForm.get('Rows') as UntypedFormArray;
    rows.push(this.createOtherActivityRow(newActivity));
    this.updateOtherActivitiesDataSource();
  }

  // Add method to delete a row
  deleteOtherActivityRow(index: number) {
    const rows = this.otherActivitiesForm.get('Rows') as UntypedFormArray;
    rows.removeAt(index);
    this.updateOtherActivitiesDataSource();
  }

}

