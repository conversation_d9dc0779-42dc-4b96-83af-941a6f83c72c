import { Component, ViewChild, TemplateRef } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { SeasiteService } from 'src/app/sea/services/seasite.service';
import { MatDialog } from '@angular/material/dialog';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { DisableGlobalLoaderService } from 'src/app/shared/services/disable-global-loader.service';
import { SeaLogicalQuestionComponent } from '../sea-logical-question/sea-logical-question.component';
import { TranslateService } from '@ngx-translate/core';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { SodexosocialService } from '../../services/sodexosocial.service';
import { CsvfilestatusService } from '../../services/csvfilestatus.service';
import * as moment from 'moment';


@Component({
  selector: 'app-sea-question',
  templateUrl: './sea-question.component.html',
  styleUrls: ['./sea-question.component.scss']
})
export class SeaQuestionComponent {
  @ViewChild('unSavedQuestion') unSavedQuestion: TemplateRef<any>;
  // @ViewChild('seaLogicalQuestion') seaLogicalQuestion: TemplateRef<any>;
  isUnsaved = false;
  perPage = 4;
  totalPage = 0;
  currentPage = 0;
  topicFullQuestions = [];
  topicNonlogicQuestions = [];
  topicLogicQuestions = [];
  topicFullQuestionsView = [];
  topicLogicalFullQuestionsView: any = [];
  topicLogicalQuestions = [];
  updateInfo: any;
  topicDisplay = {
    levelOne: '',
    levelTwo: '',
    levelThree: ''
  }
  pageNumber = 1;
  totalPages = 1;
  currentTopicQuestions = [];
  profileLang: any;
  defaultLanguage = 'en';
  payloadData = [];
  initialQuestions: string;
  logicalType: string;
  view: any;
  logicalParent: any;
  currentLogicalQuestion: any;
  // topicLogicalQuestions: any;
  dialogRef1: any;
  dialogRef2: any;
  showLoaderText: boolean;
  QuestionUnSavedModel: any;
  savedQuestions = [];
  answered: any;
  modifiedQuestions = 0;
  sitedata: any;
  isLoading: boolean;
  questionUpdatedBy: any;
  nextQuestion: boolean = false;
  topicLogicalAnswer: any;
  questionLogicalAnswer: any;
  siteId: any;
  numItems: any;
  payloadLoading = false;
  currentFile: File;
  fileName: string = '';
  constructor(
    private Site: SeasiteService,
    private dialogRef: MatDialogRef<SeaQuestionComponent>,
    private dialog: MatDialog,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private dgls: DisableGlobalLoaderService,
    private translate: TranslateService,
    private lts: LanguageTranslateService,
    private sc: SodexosocialService,
    private fu: CsvfilestatusService,
  ) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
  }

  ngOnInit() {
    this.topicLogicQuestions.forEach(q => {
      try {
        q.answer = JSON.parse(q.answer);
        q.options = JSON.parse(q.options);
      } catch (e) {
        q.answer = q.answer;
        q.options = q.options;
      }
    })
    // this.topicLogicQuestions = this.topicFullQuestions.filter( (k) { return k.isLogical })=>;
    this.sitedata = this.Site.seaSiteDetails;
    this.profileLang = this.Site.seaSiteDetails.profile.languageIdentifier;
    this.questionUpdatedBy = this.Site.seaSiteDetails.profile.firstName ? this.Site.seaSiteDetails.profile.firstName + ' ' + this.Site.seaSiteDetails.profile.lastName : 'Sodexo User';
    this.perPage = 4;
    this.prepareView();
    this.numItems = this.topicFullQuestionsView.length;
    this.totalPages = Math.ceil(this.numItems / this.perPage);
    this.currentPage = 1
  }
  getCheckBoxAnswerValue(answer, question, index) {
    if (question.lang) {
      try {
        return question.lang[this.profileLang || 'en'].options[index].name
      } catch (e) {
        return answer.name
      }
    } else {
      return answer.name
    }
  }
  calculatePage() {
    const totalQuestions = this.topicFullQuestionsView.length;
    // var pageAllSize = totalQuestions;
    // if (totalQuestions == 4 || totalQuestions < 4) {
    //   pageAllSize = 4;
    // }else if(totalQuestions == 8 || totalQuestions < 8){
    //   pageAllSize = 8;
    // }

    // this.totalPage = Math.ceil(totalQuestions / this.perPage);
    this.currentPage = 1;
  }
  closeDialog() {
    this.closeUnsaveQuestionModel();
    this.payloadData = [];
    this.dialogRef.close();
  }

  getCurrentQuestions(condition: boolean) {
    // forward
    if (condition) {
      this.currentPage += 1;
    } else {
      // backward 
      this.currentPage -= 1;
    }
  }

  getparseData(data: any, questionType?: any) {
    let parseData: any;
    if (data) {
      try {
        parseData = JSON.parse(data.answer);
      } catch (error) {
        parseData = data.answer;
      }
    } else {
      parseData = [];
    }
    return parseData;
  }
  getParseDataCheckbox(data: any, questionType?: any) {
    if (!data.answer) {
      return [];
    }
    let parseData: any;
    if (data) {
      try {
        parseData = JSON.parse(data.answer);
      } catch (error) {
        parseData = data.answer;
      }
    } else {
      parseData = [];
    }
    return parseData;
  }
  checkboxValidation(data: any) {
    if (data.isRequired) {
      var validationState = data.answer.some((k: any) => {
        return k.value
      });
      return !validationState;
    } else {
      return false;
    }
  }

  onInput(question: any, e: any) {
    this.changePayload(question, (event.target as HTMLInputElement).value);
  }

  changePayload(data: any, option: any) {
    
    var isExist = this.payloadData?.some((item) => {
      return item.id == data.id;
    });
    if (!isExist) {
      this.payloadData.push(data);
    } else if ((data.questionType == 'toggle' || data.questionType == 'radio') && !data.isLogical) {
      var logicalQData = this.payloadData.find((element) => {
        return element.currentLogicalId == data.id;
      })
      if (!!logicalQData) {
        this.restoreLogicalQuestion(logicalQData.id)
        this.removeById(logicalQData);

      }
    }
    if ((data.questionType == 'toggle' || data.questionType == 'radio') && !data.isLogical && !!option.logicQid) {
      if (!!data.questionResultId && data.logicalId == option.logicQid) {
        data.currentLogicalId = option.logicQid;
      } else if (data.currentLogicalId !== option.logicQid) {
        data.currentLogicalId = null;
      }

      this.openLogicalModal(data, option.logicQid, false)
    } else if ((data.questionType == 'toggle' || data.questionType == 'radio') && !data.isLogical && !option.logicQid) {
      data.currentLogicalId = null;
    }

    if (data.questionType == 'checkbox') {
      if (option.name == 'None' && option.value) {
        data.answer = data.answer.map((i) => {
          if (i.name !== 'None') {
            i.value = false;
          }
          return i;
        });
      } else if (option.name !== 'None') {
        var noneData = data.answer.find((k) => {
          if (k.name == 'None' && k.value)
            return k;
        });
        if (!!noneData) {
          noneData.value = false;
        }
      }
    }

    if (data.questionType == 'slider') {
      data.answer = data.sliderOptions.stepsArray;
      data.answer.forEach((item, index) => {
        if (index == (parseInt(option) - 1)) {
          data.hint = item;
          data.value = data.hint.value;
        }
      })
    }

    // if (data.questionType == 'file upload') {
    //   this.payloadData.push(data.file);
    // }

    // if (data.questionType === 'radio') {
    //   if (option.name) {
    //     data.answer = data.answer.map((i) => {
    //       return {
    //         ...i, // Keep all other keys
    //         value: option.name.includes(i.name) // Set value to true if name matches, otherwise false
    //       };
    //     });
    //   }
    //   this.payloadData = [...data];
    // }
  }
  async saveQuestions(isChanged) {
    console.log('ff')
    var answeredQuestions = 0;
    var questionPayload = this.payloadData.filter((item) => {
      if (!item.isLogical && (item.questionType == 'toggle' || item.questionType == 'radio')) {
        return item;
      }
    });

    if (questionPayload.length > 0) {
      var questionPayloadData = questionPayload.map((k) => {
        return { id: k.id, logicalId: k.currentLogicalId || null }
      })

      if (questionPayloadData.length > 0) {
        this.dgls.load();
        this.Site.updateBulkQuestion(questionPayloadData).subscribe(res => {
          this.dgls.unload();
          this.updateQuestionId(res);
          questionPayload = []
        }, (e) => {
          questionPayload = []
        });
      }
    }

    const payload = await Promise.all(this.payloadData.map(async (data) => {
      let finalData: any = {
        questions: data?.id || '',
        site: this.sitedata?.sites?.id || '',
        topic: data?.topic || '',
        updatedBy: this.questionUpdatedBy || '',
      };
  
      if (!!data.questionResultId) {
        finalData.id = data.questionResultId;
      } else {
        if (!data.isLogical) {
          answeredQuestions++;
        }
      }
  
      if (data.questionType === 'radio' || data.questionType === 'toggle') {
        finalData.answer = data.answer.map((item) => {
          item.value = item.name.trim() === data.value.trim();
          return item;
        });
        const score = data.value
          ? data.answer.find((item) => item.value === true)
          : 0;
        finalData.score = score?.points || 0;
      }
  
      else if (data.questionType === 'slider') {
        finalData.answer = data.answer.map((item) => {
          item.isAnswer = item.value === data.value;
          return item;
        });
        finalData.score = data.hint?.score || 0;
      }
  
      else if (data.questionType === 'numeric') {
        finalData.answer = data.answer;
        finalData.score = data.answer
          ? data.answer.reduce((sum, ans) => {
              if (ans.value == 0) sum = 0;
              else sum += ans.points;
              return sum;
            }, 0)
          : 0;
      }
      //  else if(data.questionType === 'file upload') {
      //    const formData: FormData = new FormData();
      //    formData.append('file', data.file);  // Appending the file
      //    formData.append('questionType', data.questionType); // Appending metadata if needed
      //   finalData.answer = [{value: "https://aziest1jwj794.blob.core.windows.net/files/23%2001%2002_Carbon%20Footprint%20for%20Food%20Services_Technical%20requirements_v8.xlsx?sv=2025-05-05&se=2035-04-22T13%3A26%3A33Z&sr=b&sp=r&sig=8d%2BsS3ncpDfAlVfPv6Td7%2FXW0SRRnTgWBJTZtkLYqBI%3D"}];
      //   finalData.answer[0].description = "";
      //   finalData.answer[0].lang = null;
      //   finalData.answer[0].logicQid = null;
      //   finalData.answer[0].name = "";
      //   finalData.answer[0].points = null;
      //   finalData.score = data.answer ? data.answer.reduce((sum, ans) => {
      //     if (ans.value == 0) { sum = 0 } else { sum = sum + ans.points }
      //     return sum;
      //   }, 0) : 0;
      //   finalData.questions = data.question.id;
      //   finalData.topic = data.question.topic; 

      //   const payload = {
      //     url: "https://aziest1jwj794.blob.core.windows.net/files/23%2001%2002_Carbon%20Footprint%20for%20Food%20Services_Technical%20requirements_v8.xlsx?sv=2025-05-05&se=2035-04-22T13%3A26%3A33Z&sr=b&sp=r&sig=8d%2BsS3ncpDfAlVfPv6Td7%2FXW0SRRnTgWBJTZtkLYqBI%3D",
      //     site: this.sitedata.sites?.id,
      //     siteId: this.sitedata.sites?.siteId,
      //     siteName: this.sitedata.sites?.name,
      //     Date: moment(new Date()).format("MMMM Do YYYY, HH:mm"),
      //     blobname: data.file?.name,
      //     downloadedBy: this.sitedata.profile?.email,
      //     fileType: data?.file?.name.split('.').pop(),
      //     questionId: data.question?.questionId 
      //   }

      //   this.fu.postFileUploads(payload).subscribe({
      //     next: (res) => {
      //       this.toaster.success('File info stored on Database');
      //     },
      //     error: (err) => {
      //       console.error('Error saving file info:', err);
      //       this.toaster.error('Failed to store file info in Database');
      //     }
      //   }); 
      //  }  

      else if (data.questionType === 'file upload') {
        finalData.answer = [{ value: '' }];
        await new Promise<void>((resolve, reject) => {
          const formData: FormData = new FormData();
          formData.append('file', data.file);
          formData.append('questionType', data.questionType);
  
          this.sc.uploadSftpFile(formData, { type: 5 }).subscribe({
            next: (result: any) => {
              finalData.answer[0].value = result || '';
              finalData.answer[0].description = '';
              finalData.answer[0].lang = null;
              finalData.answer[0].logicQid = null;
              finalData.answer[0].name = '';
              finalData.answer[0].points = null;
  
              finalData.score = data.answer
                ? data.answer.reduce((sum, ans) => {
                    if (ans.value == 0) sum = 0;
                    else sum += ans.points;
                    return sum;
                  }, 0)
                : 0;
  
              finalData.questions = data.question.id;
              finalData.topic = data.question.topic;
  
              this.toaster.success('Your file is uploaded successfully');

              const payload = {
                url: result,
                site: this.sitedata.sites?.id,
                siteId: this.sitedata.sites?.siteId,
                siteName: this.sitedata.sites?.name,
                Date: moment(new Date()).format("MMMM Do YYYY, HH:mm"),
                blobname: decodeURIComponent(result.split('/files/')[1].split('?')[0]) || data.file?.name,
                downloadedBy: this.sitedata.profile?.email,
                fileType: data?.file?.name.split('.').pop(),
                questionId: data.question?.questionId 
              }

              this.fu.postFileUploads(payload).subscribe({
                next: (res) => {
                  this.toaster.success('File info stored on Database');
                },
                error: (err) => {
                  console.error('Error saving file info:', err);
                  this.toaster.error('Failed to store file info in Database');
                }
              }); 
              
              resolve();
            },
            error: (err) => {
              console.error("File upload failed", err);
              this.toaster.error('Your file is not uploaded');
              resolve(); // resolve anyway to avoid blocking
            },
          });
        });
      }
      else {
        finalData.answer = data.answer;
        finalData.score = data.answer
          ? data.answer.reduce((sum, ans) => {
              if (!!ans.value) sum += ans.points;
              return sum;
            }, 0)
          : 0;
      }
  
      return finalData;
    }));

    if (payload && payload.length) {
      this.Site.questionResults(payload).subscribe((res: any) => {
        this.answered = this.answered + answeredQuestions;
        this.modifiedQuestions = this.modifiedQuestions + (payload.length - answeredQuestions);
        this.payloadData = [];
        this.payloadLoading = true;
        payload.map((item) => {
          var payload: any = {
            id: item.id,
            topic: item.topic,
            score: item.score,
            questions: item.questions,
            site: item.site,
            parentTopic: item.parentTopic
          };
          (this.savedQuestions || []).push(payload)
        }, (e) => {
          this.spinner.hide();
        });
        this.spinner.hide();
        if (this.totalPages != this.currentPage) {
          this.getCurrentQuestions(true);
        } else {
          this.scoreCalculation();
          // this.closeQuestionModel();
        }
        this.toaster.success('Answers Saved Successfully');
        this.closeUnsaveQuestionModel()
      });
    } else {
      this.answered = this.answered + answeredQuestions;
      this.modifiedQuestions = this.modifiedQuestions + (payload.length - answeredQuestions);
      this.payloadData = [];
      this.isLoading = false;
      if (this.totalPages != this.currentPage) {
        this.getCurrentQuestions(true);
      } else {
        this.closeQuestionModel();
      }
    }
  }
  updateQuestionId(data) {
    data.forEach((k) => {
      (this.topicFullQuestions || []).map((i) => {
        if (i.id == k.id) {
          i.logicalId = k.logicalId;
          i.currentLogicalId = k.logicalId;
          return i;
        }
      });
    });
    this.initialQuestions = JSON.stringify(this.topicFullQuestions);
  }
  openLogicalModal(data, id, view) {
    let topicaLogical = this.topicLogicQuestions

    this.logicalType = 'questions';
    this.view = view;
    this.logicalParent = data;
    this.currentLogicalQuestion = topicaLogical?.find((item) => {
      return item.id == id;
    });
    if (this.currentLogicalQuestion !== undefined) {
      this.openlogicalQuestionsModal(data);
    }
  }
  openlogicalQuestionsModal(data, option?) {
    const SeaLogicalQuestion = this.dialog.open(SeaLogicalQuestionComponent, {
      disableClose: true,
      hasBackdrop: true,
      height: '100%',
      width: '30%',
      maxWidth: '35vw',
      panelClass: 'logicalQuestionDialog',
      // maxHeight: '100%',
    });
    SeaLogicalQuestion.componentInstance.topicLogicalQuestions = this.currentLogicalQuestion;
    SeaLogicalQuestion.componentInstance.logicalType = this.logicalType;
    SeaLogicalQuestion.componentInstance.logicalParent = this.logicalParent;
    SeaLogicalQuestion.componentInstance.payloadData = this.payloadData;
    SeaLogicalQuestion.componentInstance.currentLogicalQuestion = this.currentLogicalQuestion;
    SeaLogicalQuestion.componentInstance.initialQuestions = this.initialQuestions;
  }
  closeLogicalQuestion() {
    this.dialogRef1.close();
  }
  restoreLogicalQuestion(id: any) {
    var tempQuestions = JSON.parse(this.initialQuestions);
    var data = tempQuestions.find((i) => {
      if (i.id == id) {
        return i;
      }
    });
    var logicalQData = this.topicLogicalQuestions.find((k) => {
      if (k.id == id) {
        return k;
      }
    })
    if (!!data) {
      logicalQData.answer = data.answer;
      logicalQData.logicalId = data.logicalId;
      logicalQData.currentLogicalId = data.currentLogicalId;
      if (logicalQData.questionType === 'radio' || logicalQData.questionType === 'slider' || logicalQData.questionType === 'toggle') {
        if (logicalQData.questionType === 'slider') {
          logicalQData.hint = data.hint;
        }
        logicalQData.value = data.value;
      }
      return logicalQData;

    }
  }
  removeById(data) {
    var index = this.payloadData.findIndex((item) => {
      return item.id == data.id;
    });
    if (index > -1) {
      this.payloadData.splice(index, 1);
    }
  }
  parseData(answer) {
    var data;
    try {
      answer.answer = JSON.parse(answer.answer);
    } catch (e) {
      answer.answer = answer.answer;
    }
    if (answer) {

      if (answer.answer && answer.answer.length) {
        data = (answer.answer).filter(val => val.value);
        data = data[0].name;
      } else {
        data = ''
      }
    } else {
      data = ''
    }
    return data;
  }

  radioValidation(data: any) {
    if (data.isRequired) {
      return true;
    } else {
      return false;
    }
  }

  prepareView() {
    const modifiedFullQuestions = this.topicFullQuestions.map((question) => {
      question.bindingNativeLanguageValue = {}
      question.bindingNativeLanguageValue.content = question.lang && question.lang[this.profileLang] ? question.lang[this.profileLang].content : (question.lang['en']?.content || question.content);
      question.bindingNativeLanguageValue.hint = question.lang && question.lang[this.profileLang] ? question.lang[this.profileLang].hint : (question.lang['en']?.hint?.description || question.description);
      question.bindingNativeLanguageValue.options = question.lang && question.lang[this.profileLang] ? question.lang[this.profileLang].options : (question.lang['en']?.options || question.options);

      return this.formatQuestionAnswer(question);
    });
    this.topicFullQuestionsView = [...modifiedFullQuestions];

    // this.calculatePage();
  }

  formatQuestionAnswer(question) {
    let parsedAnswer: any = '';
    let defaultValue = '';
    const questionType = question.questionType;
    const answer = question.answer;
    const language = question.language;
    let options = [];
    let parsedLanguage = {};
    try {
      options = JSON.parse(question.options);
    } catch (e) {
      options = question.options;
    }
    try {
      parsedLanguage = JSON.parse(language);
    } catch (e) {
      parsedLanguage = language;
    }

    switch (questionType) {
      case 'checkbox':
        if (!answer) {
          parsedAnswer = options.map((option, index) => {
            option.points = options[index].points;
            option.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
            option.value = false;
            return option;
          });
          break;
        }
        let parsedCheckAns = [];
        try {
          parsedCheckAns = JSON.parse(answer);
        } catch (error) {
          parsedCheckAns = answer;
        }
        parsedAnswer = parsedCheckAns.map((ans, index) => {
          ans.points = options[index].points;
          ans.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
          return ans;
        });
        break;
      case 'radio':
      case 'toggle':
        if (!answer) {
          parsedAnswer = options.map((option, index) => {
            option.points = options[index].points;
            option.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
            option.value = false;
            return option;
          });
          break;
        }
        let parsedRAns = [];
        try {
          parsedRAns = JSON.parse(answer);
        } catch (error) {
          parsedRAns = answer;
        }
        parsedAnswer = parsedRAns.map((ans, index) => {
          ans.points = options[index].points;
          ans.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
          return ans;
        });
        var answered = parsedAnswer.find((it) => {
          return it.value === true
        })
        defaultValue = answered ? answered.name : '';
        break;
      case 'numeric':
      case 'paragraph':
      case 'short answer':
      // case 'file upload':
        if (!answer) {
          parsedAnswer = options.map((option, index) => {
            option.points = options[index].points;
            option.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
            option.value = '';
            return option;
          });
          break;
        }
        let parsedNumAns = [];
        try {
          parsedNumAns = JSON.parse(answer);
        } catch (error) {
          parsedNumAns = answer;
        }
        parsedAnswer = parsedNumAns.map((ans, index) => {
          ans.points = options[index].points;
          ans.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
          return ans;
        });
        break;
      case 'file upload':
        if(answer){
          parsedAnswer = answer.map(optn => optn);
        }
      break;
      case 'slider':
        if (!answer) {
          parsedAnswer = options.map((option, index) => {
            option.points = options[index].points;
            option.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
            option.value = '';
            return option;
          });
          break;
        }
        let parsedSAns = [];
        try {
          parsedSAns = JSON.parse(answer);
        } catch (error) {
          parsedSAns = answer;
        }
        parsedAnswer = parsedSAns.map((ans, index) => {
          ans.points = options[index].points;
          ans.lang = language[this.profileLang] ? language[this.profileLang] : language['en'];
          return ans;
        });
        var answered = parsedAnswer.findIndex((item, index) => {
          return item.value === question.value
        })
        defaultValue = answered >= 0 ? answered + 1 : question.value;
        break;
      default:
    }
    question.answer = parsedAnswer;
    question.value = defaultValue;
    return question;
  }

  extractFileName(url) {
    // Extract the file name from the URL by splitting it at each '/' and getting the last part
    const urlParts = url.split('/');
    let fileName = urlParts[urlParts.length - 1];  // Get the last part of the URL
  
    // Decode the URL-encoded characters (e.g., %20 for space)
    fileName = decodeURIComponent(fileName);
  
    // Remove the query string part (after ?)
    fileName = fileName.split('?')[0];
  
    return fileName;
  }

  selectFile(event: any, question: any): void {

    if (event.target.files && event.target.files[0]) {
      const file: File = event.target.files[0];
      this.currentFile = file;
      this.fileName = this.currentFile.name;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('questionType', 'file upload');

      const ob = {
        questionType: 'file upload', 
        file: file,
        formData: formData,
        question: question
      }
      this.payloadData.push(ob);
    } else {
      this.fileName = 'Select File';
    }
    // this.changePayload(question, this.fileName);
  }
  
  cancelSelectedFile() {
    this.fileName = 'Select File';
  }

  closeQuestions() {
    if (this.payloadData.length > 0) {
      this.showLoaderText = false;
      this.openQuestionUnSavedModel();
    } else {
      this.scoreCalculation();
      // this.closeQuestionModel();
    }
  }
  openQuestionUnSavedModel() {
    this.isUnsaved = true;
    this.QuestionUnSavedModel = this.dialog.open(this.unSavedQuestion, {
      disableClose: true,
      hasBackdrop: true,
      height: 'fit-content',
      width: 'fit-content',
      maxHeight: 'fit-content',
      maxWidth: 'fit-content',
      panelClass: 'logFile',
    });
  }
  closeUnsaveQuestionModel() {
    if (!this.payloadLoading) {
      window.location.reload();
    }
    if(this.isUnsaved){
      this.QuestionUnSavedModel.close();
    }
  }
  scoreCalculation() {
    if (this.savedQuestions && this.savedQuestions.length) {
      this.showLoaderText = true;
      var payload = {
        site: this.sitedata.sites.id,
        data: this.savedQuestions || []
      }
      this.Site.updateScorecalculation(payload).subscribe(res => {
        // window.location.reload();
        this.closeQuestionModel();
        this.savedQuestions = []
      }, (e) => {
        // window.location.reload();
        this.closeQuestionModel();
      });
    } else {
      this.closeQuestionModel();
    }
  }
  closeQuestionModel() {
    if (this.payloadLoading) {
      window.location.reload();
    }
    if (this.isUnsaved) {
      this.closeDialog()
    } else {
      this.dialogRef.close();
    }
    this.spinner.hide();
  }
  updateQuestions(data) {
    data.forEach((k) => {
      this.topicFullQuestions.map((i) => {
        if (i.id == k.questions) {
          i.questionResultId = k.id;
          i.updatedBy = k.updatedBy;
          i.updatedAt = k.updatedAt;
          return i;
        }
      });
    });
    this.topicLogicalQuestions = this.topicFullQuestions.filter((k) => { return k.isLogical });
    this.initialQuestions = JSON.stringify(this.topicFullQuestions);

  }
  checkQuestionType(questionType) {
    if (questionType == 'checkbox' || questionType == 'numeric' || questionType == 'toggle' ||
      questionType == 'paragraph' || questionType == 'slider' || questionType == 'radio' || questionType == 'short answer' || questionType == 'info'|| questionType == 'file upload' ) {
      return true;
    } else {
      return false;
    }
  }
  getQuestiondata(question: any, index) {
    var languageOptions: any = '';
    try {
      if (question.lang[this.profileLang]) {
        languageOptions = question.lang[this.profileLang].options[index];
      } else {
        languageOptions = question.lang['en'].options[index];
      }
    } catch (e) {
      languageOptions = question.lang['en'].options[index];
    }
    if (question.questionType == 'slider') {
    }
    return languageOptions;
  }
  getInfoUrl(info) {
    if (info.name.includes('http')) {
      return window.open(info.name, '_blank');
    } else {
      return window.open(`https://${info.name}`, '_blank');
    }
  }
}
