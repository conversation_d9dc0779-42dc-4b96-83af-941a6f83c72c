import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { LanguageService } from 'src/app/shared/services/language.service';
import { ManagersService } from './../../../shared/services/managers.service';
import { Component, OnInit, ViewChild, OnDestroy, ElementRef } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators, NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { SiteService } from 'src/app/shared/services/site.service';
// import { GooglePlaceDirective } from 'ngx-google-places-autocomplete';
import { Observable, Subject, merge, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { NgxPermissionsService } from 'ngx-permissions';
import { GoogleMapsModule } from '@angular/google-maps';

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.scss']
})
export class AddComponent implements OnInit, OnDestroy {
  submitted = false;
  visibilityType = 'password';
  showPassword = false;
  addForm: UntypedFormGroup;
  languages: any;
  showCredentials = false;
  showRole = false;
  sites: any;
  addressData: any;
  geoLocation: any;
  addressRemove = false;
  showHelper = false;
  noRecordsFound = false;
  @ViewChild('addressText', { static: false }) addressText: any;
  // @ViewChild('placesRef', { static: false }) placesRef: GooglePlaceDirective;
  @ViewChild('addressInput', { static: true }) addressInput!: ElementRef;
  @ViewChild('instance', { static: true }) instance: NgbTypeahead;
  // @ViewChild('manager', { static: true }) managerElement: ElementRef;
  focus$ = new Subject<string>();
  click$ = new Subject<string>();
  @ViewChild('instance', { static: true }) instanceLang: NgbTypeahead;
  focusLang$ = new Subject<string>();
  clickLang$ = new Subject<string>();
  subscription: Subscription = new Subscription();
  role: any;
  isSubAdmin = false;
  siteConfig = {
    displayKey: 'customName',
    search: true,
    placeholder: 'Choose Sites',
    searchPlaceholder: 'Search Sites',
    height: '400px'
  };

  constructor(
    private formBuilder: UntypedFormBuilder,
    private managerService: ManagersService,
    private languageService: LanguageService,
    private router: Router,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private siteService: SiteService,
    private permissionsService: NgxPermissionsService,
  ) { }

  ngOnInit() {
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    this.role = [{
      name: 'Sub-Admin',
      value: 'SubAdmin'
    },
    {
      name: 'Admin',
      value: 'SuperAdmin'
    },
    {
      name: 'Site Manager',
      value: ''
    },
    {
      name: 'Approver',
      value: 'Approver'
    },
    {
      name: 'Auditor',
      value: 'Auditor'
    }];
    console.log('this.role', this.role);
    this.permissionsService.loadPermissions(this.role);
    console.log("this.permissionsService", this.permissionsService);
    //   this.http.get('url').subscribe((permissions) => {
    //     //const perm = ["ADMIN", "EDITOR"]; example of permissions
    //     this.permissionsService.loadPermissions(permissions);
    //  })
    this.createForm();
    this.getLanguages();
    this.getSites();
  }

  createForm() {
    const nonWhitespaceRegExp = new RegExp('\\S');
    const emailRegex = new RegExp('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$');
    this.addForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.pattern(nonWhitespaceRegExp)]],
      lastName: ['', [Validators.required, Validators.pattern(nonWhitespaceRegExp)]],
      email: ['', [Validators.required, Validators.pattern(emailRegex)]],
      language: ['', [Validators.required, Validators.pattern(nonWhitespaceRegExp)]],
      languageIdentifier: [''],
      site: [''],
      active: [true, Validators.required],
      phone: '',
      address: '',
      building: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      street: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      state: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      country: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      zip: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      reportsToFirstName: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      reportsToLastName: ['', [Validators.pattern(nonWhitespaceRegExp)]],
      reportsToEmail: ['', [Validators.pattern(emailRegex)]],
      role:  ['']
    });
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.addForm.patchValue({
        country: sessionStorage.getItem('country'),
        role: ''
      });
    }
  }

  getSites() {
    const countryCondition = this.isSubAdmin ? `country=${sessionStorage.getItem('country')}` : '';
    const site$ = this.siteService.getdropdownlist(countryCondition).subscribe((res: any) => {
      res.rows.map(i => {
        i.customName = `${i.siteId}-${i.name}`;
        return i;
      });
      this.sites = res.rows;
    }, error => {
      throw error;
    });
    this.subscription.add(site$);
  }

  get f() {
    return this.addForm.controls;
  }

  search = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.click$.pipe(filter(() => !this.instance.isPopupOpen()));
    const inputFocus$ = this.focus$;
    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.sites.filter(item =>
          // tslint:disable-next-line: max-line-length
          (item.siteId + '-' + item.name + '-' + item.address.country).toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  resultFormatBandListValue(value: any) {
    return (value.siteId + '-' + value.name + (value.address.country ? '-' + value.address.country : ''));
  }

  inputFormatBandListValue(value: any) {
    if (value.siteId) {
      return (value.siteId + '-' + value.name + (value.address.country ? '-' + value.address.country : ''));
    }
    return value;
  }

  searchLanguage = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickLang$.pipe(filter(() => !this.instanceLang.isPopupOpen()));
    const inputFocus$ = this.focusLang$;
    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.languages.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.name.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  resultFormatBandListLanguage(value: any) {
    return value.name;
  }

  inputFormatBandListLanguage(value: any) {
    if (value.name) {
      return value.name;
    }
    return value;
  }

  getLanguages() {
    const languages$ = this.languageService.getRecords().subscribe((res: any) => {
      this.languages = res.filter((item) => {
        return item.isActive === true;
      });
    }, error => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(languages$);
  }

  listRecord() {
    this.router.navigate(['admin/managers'], {
      queryParams: {
        limit: 10,
        offset: 0,
        search: '',
        sortName: 'createdAt',
        sortValue: 'desc'
      }
    });
  }
  passwordVisibility() {
    this.showPassword = !this.showPassword;
    this.visibilityType = this.showPassword ? 'text' : 'password';
  }

  create(value: NgForm) {
    this.submitted = true;
    this.spinner.show();
    if (this.addForm.invalid) {
      this.spinner.hide();
      return;
    } else {
      let address;
      if (this.addForm.value.building === '' && this.addForm.value.street === '' && this.addForm.value.state === '' &&
        this.addForm.value.country === '' && this.addForm.value.zip === '') {
        address = {};
      } else {
        address = {
          building: this.addForm.value.building,
          street: this.addForm.value.street,
          state: this.addForm.value.state,
          country: this.addForm.value.country,
          zip: this.addForm.value.zip,
          geo_point: this.geoLocation
        };
      }
      const payload = {
        firstName: this.addForm.value.firstName,
        lastName: this.addForm.value.lastName,
        email: this.addForm.value.email,
        phone: this.addForm.value.phone,
        isActive: this.addForm.value.active,
        language: this.addForm.value.language.id,
        languageIdentifier: this.addForm.value.language.identifier,
        sites: this.addForm.value.site.length ? this.addForm.value.site.map(i => i.id) : [],
        ReportsToFirstName: this.addForm.value.reportsToFirstName,
        ReportsToLastName: this.addForm.value.reportsToLastName,
        ReportsToEmail: this.addForm.value.reportsToEmail,
        role: !!this.addForm.value.role ? this.addForm.value.role : null,
        address,
        country: this.addForm.value.country,
      };
      const manager$ = this.managerService.createRecord(payload).subscribe((res) => {
        this.spinner.hide();
        this.toaster.success('Manager added successfully');
        this.listRecord();
      }, error => {
        this.spinner.hide();
        if (error.error.data.message === 'Would violate uniqueness constraint-- a record already exists with conflicting value(s).') {
          this.toaster.error('Site Manager Already Exists');
        } else {
          throw error;
        }
      });
      this.subscription.add(manager$);
    }
  }
  getPlaceAutocomplete() {
    const address = this.addressText.nativeElement;
    const addressText = this.addressText.nativeElement.value;
    if (addressText && addressText.length > 0) {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({
        address: address.value,
      }, (results, status) => {
        if (!!this.addForm.value.address && status && (results == null || results.length === 0)) {
          this.showHelper = true;
          if (this.addressRemove) {
            this.noRecordsFound = true;
          }
        } else {
          this.showHelper = false;
        }
      });

    } else {
      this.addForm.patchValue({
        building: '',
        street: '',
        state: '',
        country: '',
        zip: '',
      });

    }

  }

  // Google Place Autocomplete Part
  handleAddressChange(address: any) {
    let street;
    this.addressData = {};
    address.address_components.map((item) => {

      switch (item.types[0]) {
        case 'street_number':
          this.addressData.street = item.long_name;
          break;
        case 'route':
          this.addressData.route = item.long_name;
          break;
        case 'locality':
          this.addressData.locality = item.long_name;
          break;
        case 'sublocality_level_1':
          this.addressData.subLocality = item.long_name;
          break;
        case 'administrative_area_level_2':
          this.addressData.county = item.long_name;
          break;
        case 'administrative_area_level_1':
          this.addressData.state = item.long_name;
          break;
        case 'country':
          this.addressData.country = item.long_name;
          break;
        case 'postal_code':
          this.addressData.zipCode = item.long_name;
          break;
      }
    });

    this.addressData.route = this.addressData.route ? street = `${this.addressData.route}`
      : street = `${this.addressData.county}`;

    this.addressData.subLocality = this.addressData.subLocality ? street = `${this.addressData.route},${this.addressData.subLocality}`
      : street = `${this.addressData.route}`;

    this.addressData.locality = this.addressData.locality ? street = `${this.addressData.route},${this.addressData.locality}`
      : street = `${this.addressData.route}`;

    this.addressRemove = true;
    this.geoLocation = {
      lat: address.geometry.location.lat(),
      long: address.geometry.location.lng()
    };
    this.addForm.patchValue({
      building: this.addressData.street || '',
      street,
      state: this.addressData.state || '',
      country: this.addressData.country || '',
      zip: this.addressData.zipCode || '',
    });
  }

  customAddress() {
    if (this.addressRemove) {
      this.addForm.controls.address.clearValidators();
      this.addForm.controls.address.updateValueAndValidity();
    }
  }
  roleChange() {
    this.showRole = !this.showRole;
    if (this.showRole) {
      this.addForm.value.role = 'SuperAdmin';
    }
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
