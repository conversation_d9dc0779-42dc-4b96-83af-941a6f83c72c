import { Component, OnIni<PERSON>, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, TemplateRef, ElementRef } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators, UntypedFormControl, UntypedFormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { SegmentService } from 'src/app/shared/services/segment.service';
import { ServicesService } from 'src/app/shared/services/services.service';
import { Segment } from 'src/app/shared/models/segment';
import { SiteService } from 'src/app/shared/services/site.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Site } from 'src/app/shared/models/sites.model';
import { ManagersService } from 'src/app/shared/services/managers.service';
import { Manager } from 'src/app/shared/models/manager.model';
// import { GooglePlaceDirective } from 'ngx-google-places-autocomplete';
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { Observable, Subject, merge, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { ImageuploadService } from 'src/app/shared/services/imageupload.service';
import { MatDialog } from '@angular/material/dialog';
import { ApproverServiceService } from 'src/app/carbon/services/approver-service.service';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { SiteManagementService } from 'src/app/shared/services/site-management.service';
import { SystemFieldsService } from 'src/app/shared/services/system-fields.service';



@Component({
  selector: 'app-upsert',
  templateUrl: './upsert.component.html',
  styleUrls: ['./upsert.component.scss']
})
export class UpsertComponent implements OnInit, OnDestroy {
  @ViewChild('imageUpload') imageUpload: TemplateRef<any>;
  loader = false;
  datas: any;
  sampleAvator = 'https://mdbootstrap.com/img/Photos/Others/placeholder-avatar.jpg';
  chooseFile: any;
  dialogRef2: any;
  addForm: UntypedFormGroup;
  sites: Site[];
  submitted = false;
  segments: any;
  services: any;
  showIls = false;
  managers: Manager[];
  toggleButtonName: boolean;
  toggleSelectName: boolean;
  addressData: any;
  siteManagersName: any;
  id: any;
  payload: any;
  limit = 10;
  offset = 0;
  addressRemove = false;
  geoLocation: any;
  showHelper = false;
  showSiteManagers = false;
  noRecordsFound = false;
  nonWhitespaceRegExp = new RegExp('\\S');
  areaUnit = ['m2', 'Square Feet (sq ft)', 'Acres', 'Hectares'];
  buildingTypes = ['Acute Care Hospital',
    'Amusement parks',
    'Children’s Hospital',
    'Commercial centers',
    'Conference centers',
    'Construction & engineering firms',
    'Convention centers',
    'Cruising',
    'Cultural centres',
    'Defense site',
    'Dental Center',
    'Deployed troops & PKO',
    'Elementatry (5/6 - 10/11years old)',
    'Gambling',
    'Health Center',
    'High School (14/15 - 17/19 years old)',
    'Homeland Foreign site',
    'Homeland National site',
    'Hotels',
    'Immigration centres',
    'Justice site',
    'Kindergarten (3 - 5/6 years old)',
    'Large Events',
    'Mainly Office / HQ',
    'Mainly Production Manufacturing',
    'Mainly Research & Development',
    'Mainly Stores / Malls',
    'Middle (10/11 - 14/15 years old)',
    'Mining companies',
    'Monuments & museums',
    'Natural sites & parks',
    'Nursing Home',
    'Oil & gas exploration and production',
    'Oil & gas services companies',
    'Other accommodation',
    'Power companies',
    'Prisons',
    'Probation',
    'Psychiatric Behavioral Hospital',
    'Rehab. & Specialty Hospital',
    'Senior Assistied Living',
    'Senior CCRC (all Senior above)',
    'Senior Independent Living',
    'Senior Rehabilitation',
    'Senior Skilled Nursing',
    'Shows, Entertainment',
    'Spectator Sports',
    'Sports & Social Clubs',
    'University',
    'University Academic Hospital',
    'Zoos & Aquariums',
    'Sodexo Entity'];
  @ViewChild('addressText', { static: false }) addressText: any;
  // @ViewChild('placesRef', { static: false }) placesRef: GooglePlaceDirective;
  @ViewChild('addressInput', { static: true }) addressInput!: ElementRef;
  groupSegments: any;
  countrySegments: any;
  @ViewChild('instance', { static: true }) instance: NgbTypeahead;
  focusGrpSegment$ = new Subject<string>();
  clickGrpSegment$ = new Subject<string>();
  @ViewChild('instance', { static: true }) instanceCountrySegment: NgbTypeahead;
  focusCountrySegment$ = new Subject<string>();
  clickCountrySegment$ = new Subject<string>();
  @ViewChild('instance', { static: true }) instanceBuildingType: NgbTypeahead;
  focusBuildingType$ = new Subject<string>();
  clickBuildingType$ = new Subject<string>();
  @ViewChild('instance', { static: true }) instanceIla: NgbTypeahead;
  focusIla$ = new Subject<string>();
  clickIla$ = new Subject<string>();
  headQuarters = [];
  config = {
    displayKey: 'name',
    search: true,
    placeholder: 'Choose services',
    searchPlaceholder: 'Search service',
    height: '200px'
  };
  config1 = {
    displayKey: 'name',
    search: false,
    placeholder: 'Choose SEA Category',
    searchPlaceholder: 'Search SEA Category',
    height: '200px',
    width: '400px'
  };
  unitConfig = {
    displayKey: 'name',
    search: true,
    placeholder: 'Choose Unit',
    searchPlaceholder: 'Search Unit'
  };
  subscription: Subscription = new Subscription();
  pageOffset: any;
  breadCumsData: any;

  merge = {
    displayKey: 'email',
    search: true,
    placeholder: 'Choose Managers',
    searchPlaceholder: 'Search Managers',
    height: '200px'
  };
  approver = {
    displayKey: 'email',
    search: true,
    placeholder: 'Choose Approver',
    searchPlaceholder: 'Search Approver',
    height: '200px'
  };
  siteManagement = {
    displayKey: 'fieldName',
    search: true,
    placeholder: 'Choose site Management Name',
    searchPlaceholder: 'Search site Management Name',
    height: '120px'
  };
  isSubAdmin = false;
  isApprover = false;
  approverList: any;
  siteManagers: any = [];
  approverManager: any = [];
  selectedManagerList: any[] = [];
  managerInfo: any = [];
  MenagerCount = 0;
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  offsetPaginate = 0;
  ColumnMode = ColumnMode;
  showEdit = false;
  surfaceData: any = [];
  Services: any = [];
  FilterFinelSurface: any = [];
  unit: any;
  unitPayload: any = [];
  options = {
    componentRestrictions: {
      country: []
    }
  };
  managementList:any = [];
  seaCategories: any[] = [
    {id: 0, name: "SEA Lite"},
    {id: 1, name: "SEA Full"},
    {id: 2, name: "SEA Hybrid"},
  ]

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private segmentService: SegmentService,
    private serviceList: ServicesService,
    private managerList: ManagersService,
    private siteService: SiteService,
    private siteManagementService: SiteManagementService,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private imgUp: ImageuploadService,
    private dialog: MatDialog,
    private apService: ApproverServiceService,
    private systemFields: SystemFieldsService,
  ) { }

  ngOnInit() {
    this.showEdit = window.location.href.includes('edit');
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    const params$ = this.route.params.subscribe((res: any) => {
      if (res.id) {
        this.toggleButtonName = true;
        this.toggleSelectName = true;
        this.id = Number(res.id);
        this.showSiteManagers = true;
        this.patchForm();
      }
    });
    this.subscription.add(params$);
    this.pageOffset = this.route.snapshot.queryParams.offset;
    this.breadCumsData = {
      limit: 10,
      offset: this.pageOffset,
      search: '',
      sortName: 'id',
      sortValue: 'desc'
    };
    this.createForm();
    this.getSegmentRecords();
    this.getServiceRecords();
    this.getManagerRecords();
    this.getAllSites();
    this.getSiteManagementList();
  }
  getAllSites() {
    const query = `headQuarters=1&limit=-1&${this.isSubAdmin ? `country=${sessionStorage.getItem('country')}` : ''}`;
    const sites$ = this.siteService.getRecord(query).subscribe((data: any[]) => {
      if (data.length > 0) {
        this.headQuarters = data;
      }
    }, (error) => {
      throw error;
    });
    this.subscription.add(sites$);
  }
  createForm() {
    this.addForm = this.fb.group({
      id: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      name: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      active: [true, Validators.required],
      headQuarters: [false, Validators.required],
      socialValue: [false],
      lite: [''],
      hybrid: [0],
      // seaCategory: [''],
      carbon: [false],
      water: [false],
      Comments: '',
      Profile_Center_Ids: '',
      groupSegment: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      countrySegment: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      services: ['', Validators.required],
      approver: [''],
      seaType: '',
      buildingType: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      indoorSpace: '',
      waterRisk: '',
      spaceUnit: 'Square Feet (sq ft)',
      address: ['', Validators.required],
      building: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      street: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      state: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      country: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      zip: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      managers: [''],
      city: [''],
      investment: [''],
      latitude: [''],
      logitude: [''],
      sonarsiteid: [''],
      servicesFormArray: this.fb.array([]),
      SiteManagementDetails: ['', Validators.required],
    });
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.addForm.patchValue({
        country: sessionStorage.getItem('country')
      });
    }
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.addForm.controls.lite.disable();
      this.addForm.controls.carbon.disable();
      this.addForm.controls.water.disable();
      this.addForm.controls.socialValue.disable();
    }
  }

  get f() {
    return this.addForm.controls;
  }

  // Edit and Populating Site
  patchForm() {
    this.spinner.show();
    const site$ = this.siteService.getRecordById(this.id).subscribe((res: any) => {
      this.unit = res.unit;
      this.unitPayload = [];
      res.services.map((ser: any) => {
        const findValue = (res.area || []).filter((name: any) => name.id === ser.id);
        if (findValue.length) {
          findValue[0].name = ser.name;
          findValue[0].sequence = ser.sequence;
          this.unitPayload.push(findValue[0]);
        } else {
          this.unitPayload.push({
            id: ser.id,
            name: ser.name,
            sequence: ser.sequence,
            unit: res.unit,
            areaFirstField: '',
            areaSecondField: ''
          });
        }
      });
      this.unitPayload.sort((a, b) => a.sequence - b.sequence).map((list: any, index: number) => {
        const objFormGroup = this.fb.group(list);
        this.serviceForm.push(objFormGroup);
      });
      let address: any;
      let addressInfo;
      if (res.ila) {
        this.getIlaId();
        this.addForm.patchValue({
          ila: res.ila,
        });
      }
      if (res.address && Object.keys(res.address).length > 0) {
        this.addressRemove = true;
        if (!Object.values(res.address).every(o => o === '')) {
          // tslint:disable-next-line: forin
          for (const keys in res.address) {
            if (res.address[keys] === '') {
              delete res.address[keys];
            }
            if (keys === 'geo_point') {
              delete res.address[keys];
            }

          }
          address = JSON.parse(JSON.stringify(res.address, Object.keys(res.address)));
          if (address.street) {
            address.street = address.street.replace(/,\s*$/, '');
          }
          addressInfo = Object.values(res.address).toString();
        } else {
          this.addressRemove = false;
          addressInfo = '';
        }

      } else {
        this.addressRemove = false;
        addressInfo = '';
      }
      var concateAddress = '';
      if (res.address.building) {
        concateAddress += ' ' + res.address.building + ','
      }
      if(res.address.route){
        concateAddress += ' ' + res.address.route + ','
      }
      if (res.address.street) {
        concateAddress += ' ' + res.address.street + ','
      }
      if (res.address.city) {
        concateAddress += ' ' + res.address.city + ','
      }
      if (res.address.state) {
        concateAddress += ' ' + res.address.state + ','
      }
      if (res.address.country) {
        concateAddress += ' ' + res.address.country
      }
      concateAddress = concateAddress.trim();
      concateAddress = concateAddress.replace(/^,|,$/g, '');
      // const concateAddress = (res.address.building || '') + ' ' + (res.address.street || '') + ' ' + (res.address.city || '') + ' ' + (res.address.state || '') + ' ' + (res.address.country || '');
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < res.siteManagers.length; i++) {
        const element = res.siteManagers[i];
        if (element.role === 'Approver') {
          this.approverManager.push(element);
        } else {
          this.siteManagers.push(element);
        }
        this.selectedManagerList.push(element.id);
      }
      this.managerInfo = this.siteManagers;
      this.MenagerCount = this.siteManagers;
      this.addForm.patchValue({
        name: res.name,
        id: res.siteId,
        groupSegment: res.groupSegment ? res.groupSegment : null,
        countrySegment: res.countrySegment ? res.countrySegment : res.groupSegment,
        buildingType: res.buildingType,
        seaType: res.seaType,
        active: res.isActive,
        indoorSpace: res.indoorArea,
        waterRisk: res.waterRisk,
        spaceUnit: res.unit,
        address: concateAddress || addressInfo ,
        services: res.services,
        managers: this.siteManagers,
        approver: this.approverManager,
        SiteManagementDetails: res.SiteManagement,
        building: res.address && res.address?.building ?res.address?.building : "",
        street: res.address && res.address.street ? res.address.street : "",
        state: res.address && res.address.state ?res.address.state : "",
        country: res.address && res.address.country ? res.address.country : "",
        zip: res.address && res.address.zip ? res.address.zip : "",
        headQuarters: res.headQuarters,
        socialValue: res.socialValue,
        // lite: res.lite,
        lite: this.seaCategories.find(option => option.id === res.lite),
        hybrid: res.hybrid,
        carbon: res.carbon,
        water: res.water,
        Comments: res.Comments ? res.Comments : "",
        Profile_Center_Ids: res.Profile_Center_Ids ? res.Profile_Center_Ids : "",
        city: res.city ? res.city : "",
        investment: res.investment ? res.investment : "",
        latitude: res.latitude ?res.latitude : "",
        logitude: res.logitude ? res.logitude : "",
        sonarsiteid: res.sonarsiteid ? res.sonarsiteid : ""
      });
      this.siteManagersName = res.siteManagers.map(item => item.email);
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.listRecord();
      throw error;
    });
    this.subscription.add(site$);
  }

  get serviceForm() {
    return (this.addForm.get('servicesFormArray') as UntypedFormArray);
  }
  get serviceFormControls() {
    return (this.addForm.get('servicesFormArray') as UntypedFormArray).controls;
  }
  getSegmentRecords() {
    this.spinner.show();
    const segment$ = this.segmentService.getSegments(-1).subscribe(
      (res: Segment[]) => {
        this.spinner.hide();
        this.segments = res;
        this.groupSegments = this.segments.filter(item => item.type === 'Group Segment');
        this.countrySegments = this.segments.filter(item => item.type === 'Country Segment');
      },
      error => {
        this.spinner.hide();
        throw error;
      }
    );
    this.subscription.add(segment$);
  }

  getManagerRecords() {
    const query = `?${this.isSubAdmin ? `country=${sessionStorage.getItem('country')}` : ''}`;
    const managers$ = this.managerList.getsiteManagers(query).subscribe(
      (res: Manager[]) => {
        this.spinner.hide();
        this.managers = res.filter((val: any) => val.role !== 'Approver' && !(this.selectedManagerList.includes(val.id)));
        this.approverList = res.filter((val: any) => val.role === 'Approver' && !(this.selectedManagerList.includes(val.id)));
      },
      error => {
        this.spinner.hide();
        throw error;
      }
    );

    this.subscription.add(managers$);
    console.log("this.managers", this.managers);
    console.log("this.approverList", this.approverList);
  }

  getServiceRecords() {
    const services$ = this.serviceList.getRecords('isActive=true').subscribe(
      (res: any) => {
        this.spinner.hide();
        this.services = res;
      },
      error => {
        this.spinner.hide();
        throw error;
      }
    );
    this.subscription.add(services$);
  }
  searchGrpSegment = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickGrpSegment$.pipe(filter(() => !this.instance.isPopupOpen()));
    const inputFocus$ = this.focusGrpSegment$;
    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.groupSegments.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.name.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  resultSegment(value: any) {
    return value.name;
  }

  inputSegment(value: any) {
    if (value.name) {
      return value.name;
    }
    return value;
  }

  searchCountrySegment = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickCountrySegment$.pipe(filter(() => !this.instanceCountrySegment.isPopupOpen()));
    const inputFocus$ = this.focusCountrySegment$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.countrySegments.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.name.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  searchBuildingType = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickBuildingType$.pipe(filter(() => !this.instanceBuildingType.isPopupOpen()));
    const inputFocus$ = this.focusBuildingType$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.buildingTypes.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  resultBuildingType(value: any) {
    return value;
  }

  inputBuildingType(value: any) {
    return value;
  }

  searchIla = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickIla$.pipe(filter(() => !this.instanceIla.isPopupOpen()));
    const inputFocus$ = this.focusIla$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.headQuarters.filter(item =>
          // tslint:disable-next-line: max-line-length
          (item.siteId + '-' + item.name).toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  resultIla(value: any) {
    return (value.siteId + '-' + value.name);
  }

  inputIla(value: any) {
    if (value.siteId) {
      return (value.siteId + '-' + value.name);
    }
    return value;
  }

  // Adding a Site
  addSite() {
    this.submitted = true;
    if (this.addForm.invalid) {
      return;
    } else {
      let address;
      if (this.addForm.value.building === '' && this.addForm.value.street === '' && this.addForm.value.state === '' &&
        this.addForm.value.country === '' && this.addForm.value.zip === '') {
        address = {};
      } else {
        address = {
          building: this.addForm.value.building,
          street: this.addForm.value.street,
          state: this.addForm.value.state,
          country: this.addForm.value.country,
          zip: this.addForm.value.zip,
          geo_point: this.geoLocation,
          city: this.addForm.value.city,
          investment: this.addForm.value.investment,
          latitude: this.addForm.value.latitude,
          logitude: this.addForm.value.logitude
        };
      }
      // this.addForm.value.managers.push()
      if (this.addForm.value.approver.length) {
        this.addForm.value.approver.map(val => { this.addForm.value.managers.push(val); });
      }      
      this.payload = {
        name: this.addForm.value.name,
        siteId: this.addForm.value.id,
        groupSegment: this.addForm.value.groupSegment.id ? this.addForm.value.groupSegment.id : null,
        countrySegment: this.addForm.value.countrySegment ? this.addForm.value.countrySegment.id : null,
        indoorArea: this.addForm.value.indoorSpace ? this.addForm.value.indoorSpace : '',
        waterRisk: this.addForm.value.waterRisk ? this.addForm.value.waterRisk : 0,
        unit: this.addForm.value.spaceUnit === '' ? this.areaUnit[0] : this.addForm.value.spaceUnit,
        services: this.addForm.value.services.map(item => item.id),
        siteManagers: this.addForm.value.managers.map(item => item.id),
        ila: this.showIls ? this.addForm.value?.ila?.id : null,
        isActive: this.addForm.value.active,
        seaType: this.addForm.value.seaType,
        buildingType: this.addForm.value.buildingType,
        address,
        headQuarters: this.addForm.value.headQuarters,
        // hybrid: (this.addForm.value?.lite?.id  || 0) == 2 ? 1 : 0,
        Comments: this.addForm.value.Comments ? this.addForm.value.Comments : '',
        Profile_Center_Ids: this.addForm.value.Profile_Center_Ids ? this.addForm.value.Profile_Center_Ids : '',
        country: this.addForm.value.country,
        city: this.addForm.value.city,
        investment: this.addForm.value.investment,
        latitude: this.addForm.value.latitude,
        logitude: this.addForm.value.logitude,
        sonarsiteid: this.addForm.value.sonarsiteid,
        SiteManagement: this.addForm.value.SiteManagementDetails ? this.addForm.value.SiteManagementDetails?.id : null,
        socialValue: this.addForm.value.socialValue || 0,
        hybrid: this.addForm.value?.lite?.id === 2 ? this.addForm.value?.hybrid : 0,
        carbon: this.addForm.value?.carbon || 0,
        water: this.addForm.value?.water || 0,
        lite: this.addForm.value?.lite?.id || 0,
      };
      if (sessionStorage.getItem('role') === 'SubAdmin') {
        delete this.payload.socialValue;
        delete this.payload.carbon;
        delete this.payload.lite;
        delete this.payload.water;
      }
      this.spinner.show();
      if (this.id) {
        this.payload.area = this.addForm.value.servicesFormArray;
        const site$ = this.siteService.updateRecord(this.id, this.payload).subscribe(
          res => {
            this.spinner.hide();
            this.toaster.success('Site updated successfully');
            this.listRecord();
          },
          error => {
            this.spinner.hide();
            throw error;
          }
        );
        this.subscription.add(site$);
      } else {
        const site$ = this.siteService.createRecord(this.payload).subscribe(
          res => {
            this.spinner.hide();
            this.toaster.success('Site added successfully');
            this.listRecord();
          },
          error => {
            this.spinner.hide();
            throw error;
          }
        );
        this.subscription.add(site$);
      }
    }
  }

  // Handling Invalid Google address and clearing inputs
  getPlaceAutocomplete() {
    const address = this.addressText.nativeElement;
    const addressText = this.addressText.nativeElement.value;
    if (addressText && addressText.length > 0) {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({
        address: address.value,
      }, (results, status) => {
        if (!!this.addForm.value.address && status && (results == null || results.length === 0)) {
          this.showHelper = true;
          if (this.addressRemove) {
            this.noRecordsFound = true;
          }
        } else {
          this.showHelper = false;
        }
      });

    } else {
      this.addForm.patchValue({
        building: '',
        street: '',
        state: '',
        country: '',
        zip: '',
      });

    }

  }

  // Google Place Autocomplete Part
  handleAddressChange(address: any) {
    let street;
    this.addressData = {};
    address.address_components.map((item) => {
      switch (item.types[0]) {
        case 'street_number':
          this.addressData.street = item.long_name;
          break;
        case 'route':
          this.addressData.route = item.long_name;
          break;
        // case 'locality':
        //   this.addressData.locality = item.long_name;
        //   break;
        case 'sublocality_level_1':
          this.addressData.subLocality = item.long_name;
          break;
        // case 'administrative_area_level_2':
        //   this.addressData.county = item.long_name;
        //   break;
        case 'administrative_area_level_1':
          this.addressData.state = item.long_name;
          break;
        case 'country':
          this.addressData.country = item.long_name;
          break;
        case 'postal_code':
          this.addressData.zipCode = item.long_name;
          break;
      }
    });    
    // this.addressData.route = this.addressData.route ? street = this.addressData.route || "" : street = this.addressData.county;

    // this.addressData.subLocality = this.addressData.subLocality ? street = `${this.addressData.route ? this.addressData.route : ''},${this.addressData.subLocality}`
    //   : street = `${this.addressData.route ? this.addressData.route : ''}`;

    // this.addressData.locality = this.addressData.locality ? street = `${this.addressData.route ? this.addressData.route : ''},${this.addressData.locality}`
    //   : street = `${this.addressData.route ? this.addressData.route : ''}`;
    this.addressRemove = true;
    this.geoLocation = {
      lat: address.geometry.location.lat(),
      long: address.geometry.location.lng()
    };
    this.addForm.patchValue({
      building: this.addressData.street || '',
      street: this.addressData.route,
      state: this.addressData.state || '',
      country: this.addressData.country || '',
      zip: this.addressData.zipCode || '',
      latitude: address.geometry.location.lat(),
      logitude: address.geometry.location.lng(),
      city: this.addressData.state
    });
  }

  // Getting ILA Id
  getIlaId() {
    this.showIls = !this.showIls;
    if (this.showIls) {
      this.addForm.patchValue({ headQuarters: false });
      this.addForm.get('headQuarters').disable();
      this.addForm.setControl('ila', new UntypedFormControl('', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]));
    } else {
      this.addForm.removeControl('ila');
      this.addForm.get('headQuarters').enable();
    }
  }

  listRecord() {
    this.router.navigate(['admin/sites'], {
      queryParams: this.breadCumsData
    });
  }

  customAddress() {
    if (this.addressRemove) {
      this.addForm.controls.address.clearValidators();
      this.addForm.controls.address.updateValueAndValidity();
    }
    this.addForm.patchValue({
      country: sessionStorage.getItem('country')
    });
  }
  changeHq() {
    this.addForm.value.headQuarters = !this.addForm.value.headQuarters;
    if (this.addForm.value.headQuarters && this.showIls) {
      this.getIlaId();
    }
  }
  selectedValue(event: any) {
  }
  trackByFn(index: any, item: any) {
    return index;
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  fileUpload(event: any) {
    this.datas = event;
    this.chooseFile = event.target.files[0];
    // tslint:disable-next-line:prefer-const
    let reader = new FileReader();
    // tslint:disable-next-line:no-shadowed-variable
    reader.onload = (event: any) => {
      this.sampleAvator = event.target.result;
    };
    reader.readAsDataURL(event.target.files[0]);
  }
  User() {
    this.siteService.getRecordById(this.id).subscribe((res: any) => {
      if (res.ProfileUrl) {
        this.sampleAvator = res.ProfileUrl;
      }
    });
    this.dialogRef2 = this.dialog.open(this.imageUpload, {
      disableClose: true,
      hasBackdrop: true,
      height: '390px',
      width: '500px'
    });
  }
  User1() {
    if (this.datas) {
      this.loader = true;
      const formData: FormData = new FormData();
      formData.append('file', this.chooseFile);
      this.imgUp.uploadUserImage(formData, this.id).subscribe((result: any) => {
        this.loader = false;
        if (result) {
          this.toaster.success('You have uploaded a file successfully');
          this.close2();
        }
      });
    } else {
      this.toaster.warning('Please choose a File');
    }
  }
  close2() {
    this.dialogRef2.close();
  }

  getSiteManagementList(){
    this.spinner.show();
    this.systemFields.getSystemFields('Site Management').subscribe((res:any)=>{
      
      this.spinner.hide();
      this.managementList = [...res];
    },(error)=>{
    this.spinner.hide();
    })
  }
}
